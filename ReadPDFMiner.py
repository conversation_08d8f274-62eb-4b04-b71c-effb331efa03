# pip install pdfplumber
# pip install pdfminer.six

from pdfminer.high_level import extract_text

from FindWord import find_word_position


def extract_column_with_pdfminer(pdf_path, column_name=None, begin_word=None, finish_word=None):
    # Извлекаем текст из PDF файла
    text = extract_text(pdf_path)  # .replace('\n',' ')
    if begin_word and finish_word:
        start_position = text.find(begin_word)
        if start_position != -1:
            text = text[start_position:]
            finish_position = text.find(finish_word)
            if finish_position == -1:
                finish_position = find_word_position(text, finish_word)
            if finish_position:
                return text[:finish_position + 6]

    # Разбиваем текст по строкам
    lines = text.split("\n")
    for i, line in enumerate(lines):
        if column_name in line and not begin_word and not finish_word:
            return line


if __name__ == '__main__':
    # Укажите путь к вашему PDF файлу
    column_name = "IBAN"
    pdf_path = r'D:\Prestige\Python\Medoc\03180041098985J1201016100000012310920240318 (1).pdf'
    #
    # Вызов функции для извлечения данных из колонки
    print(extract_column_with_pdfminer(pdf_path, column_name))
