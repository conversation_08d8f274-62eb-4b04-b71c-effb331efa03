# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1407801"
SQL_CREATE_TABLE = f"""
  DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
  CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    id uuid DEFAULT uuid_generate_v4() NOT NULL, -- Ідентифікатор
    hdate date NULL, -- Дата Повідомлення
    hnum varchar(30) NULL, -- Номер Повідомлення
    r01g1d date NULL, -- Дата складання
    r01g21 int8 NULL, -- Номер ПН
    r01g22 varchar(10) NULL, -- Номер РК
    r01g3s varchar(35) NULL, -- Тип Документа
    r01g4 numeric(15, 2) NULL, -- Сума с ПДВ
    r01g5 numeric(15, 2) NULL, -- ПДВ
    r02g1s int8 NULL, -- ОКПО продавця
    r02g2 int8 NULL, -- ИНН продавця
    r02g3s varchar(100) NULL, -- Назва продавця
    r02g4d date NULL, -- Дата реєстрації ПДВ
    r02g5d date NULL, -- Дата припинення реєстрації ПДВ
    r03g1s int8 NULL, -- ОКПО покупця
    r03g2 int8 NULL, -- ИНН покупця
    r03g3s varchar(100) NULL, -- Назва покупця
    r03g4d date NULL, -- Дата реєстрації ПДВ
    r03g5d date NULL, -- Дата припинення реєстрації ПДВ
    m0210 numeric(1) NULL, -- письмові  пояснення
    r0210g1s varchar NULL, -- додаткова информація
    m0220 numeric(1) NULL, -- копії документів
    m0221 numeric(1) NULL, -- договорів зокрема зовнішньоекономічних контрактів, з додатками до них
    m0222 numeric(1) NULL, -- договорів,  довіреностей,  актів  керівного  органу  платника  податку
    m0223 numeric(1) NULL, -- первинних документів щодо постачання/придбання товарів / послуг
    m02231 numeric(1) NULL, -- первинних документів щодо
    m022311 numeric(1) NULL, -- постачання
    m022312 numeric(1) NULL, -- придбання
    m022313 numeric(1) NULL, -- товарів
    m022314 numeric(1) NULL, -- послуг
    m022315 numeric(1) NULL, -- зберігання продукції
    m022316 numeric(1) NULL, -- транспортування
    m022317 numeric(1) NULL, -- навантаження
    m022318 numeric(1) NULL, -- розвантаження
    m02232 numeric(1) NULL, -- складських документів
    m02233 numeric(1) NULL, -- інвентаризаційні
    m022331 numeric(1) NULL, -- рахунків-фактури
    m022332 numeric(1) NULL, -- інвойсів
    m02234 numeric(1) NULL, -- актів приймання-передачі
    m02235 numeric(1) NULL, -- накладних
    m0224 numeric(1) NULL, -- розрахункових документів
    m0225 numeric(1) NULL, -- документів відповідності, сертифікації, якості
    m02251 numeric(1) NULL, -- декларації про відповідність
    m02252 numeric(1) NULL, -- паспорти якості
    m02253 numeric(1) NULL, -- сертифікати відповідності
    r0220g1s varchar NULL, -- Додаткова інформація
    hexecutor varchar(50) NULL, -- голова комісії
    filename varchar(55) NULL,
    CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
  );
  
  COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 2. Повідомлення про необхідність надання додаткових пояснень та/або документів';
  COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
  COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
  COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
  COMMENT ON COLUMN {TABLE_NAME}.r01g1d IS 'Дата складання';
  COMMENT ON COLUMN {TABLE_NAME}.r01g21 IS 'Номер ПН';
  COMMENT ON COLUMN {TABLE_NAME}.r01g22 IS 'Номер РК';
  COMMENT ON COLUMN {TABLE_NAME}.r01g3s IS 'Тип Документа';
  COMMENT ON COLUMN {TABLE_NAME}.r01g4 IS 'Сума с ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.r01g5 IS 'ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.r02g1s IS 'ОКПО продавця';
  COMMENT ON COLUMN {TABLE_NAME}.r02g2 IS 'ИНН продавця';
  COMMENT ON COLUMN {TABLE_NAME}.r02g3s IS 'Назва продавця';
  COMMENT ON COLUMN {TABLE_NAME}.r02g4d IS 'Дата реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.r02g5d IS 'Дата припинення реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.r03g1s IS 'ОКПО покупця';
  COMMENT ON COLUMN {TABLE_NAME}.r03g2 IS 'ИНН покупця';
  COMMENT ON COLUMN {TABLE_NAME}.r03g3s IS 'Назва покупця';
  COMMENT ON COLUMN {TABLE_NAME}.r03g4d IS 'Дата реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.r03g5d IS 'Дата припинення реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.m0210 IS 'письмові  пояснення';
  COMMENT ON COLUMN {TABLE_NAME}.r0210g1s IS 'додаткова информація';
  COMMENT ON COLUMN {TABLE_NAME}.m0220 IS 'копії документів';
  COMMENT ON COLUMN {TABLE_NAME}.m0221 IS 'договорів зокрема зовнішньоекономічних контрактів, з додатками до них';
  COMMENT ON COLUMN {TABLE_NAME}.m0222 IS 'договорів,  довіреностей,  актів  керівного  органу  платника  податку';
  COMMENT ON COLUMN {TABLE_NAME}.m0223 IS 'первинних документів щодо постачання/придбання товарів / послуг';
  COMMENT ON COLUMN {TABLE_NAME}.m02231 IS 'первинних документів щодо';
  COMMENT ON COLUMN {TABLE_NAME}.m022311 IS 'постачання';
  COMMENT ON COLUMN {TABLE_NAME}.m022312 IS 'придбання';
  COMMENT ON COLUMN {TABLE_NAME}.m022313 IS 'товарів';
  COMMENT ON COLUMN {TABLE_NAME}.m022314 IS 'послуг';
  COMMENT ON COLUMN {TABLE_NAME}.m022315 IS 'зберігання продукції';
  COMMENT ON COLUMN {TABLE_NAME}.m022316 IS 'транспортування';
  COMMENT ON COLUMN {TABLE_NAME}.m022317 IS 'навантаження';
  COMMENT ON COLUMN {TABLE_NAME}.m022318 IS 'розвантаження';
  COMMENT ON COLUMN {TABLE_NAME}.m02232 IS 'складських документів';
  COMMENT ON COLUMN {TABLE_NAME}.m02233 IS 'інвентаризаційні';
  COMMENT ON COLUMN {TABLE_NAME}.m022331 IS 'рахунків-фактури';
  COMMENT ON COLUMN {TABLE_NAME}.m022332 IS 'інвойсів';
  COMMENT ON COLUMN {TABLE_NAME}.m02234 IS 'актів приймання-передачі';
  COMMENT ON COLUMN {TABLE_NAME}.m02235 IS 'накладних';
  COMMENT ON COLUMN {TABLE_NAME}.m0224 IS 'розрахункових документів';
  COMMENT ON COLUMN {TABLE_NAME}.m0225 IS 'документів відповідності, сертифікації, якості';
  COMMENT ON COLUMN {TABLE_NAME}.m02251 IS 'декларації про відповідність';
  COMMENT ON COLUMN {TABLE_NAME}.m02252 IS 'паспорти якості';
  COMMENT ON COLUMN {TABLE_NAME}.m02253 IS 'сертифікати відповідності';
  COMMENT ON COLUMN {TABLE_NAME}.r0220g1s IS 'Додаткова інформація';
  COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';
    
"""


async def main_t_tax_cabinet_J1407801_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_J1407801_async())
