# работа c НН(налоговая накладная) и РК(расчет корректировка(т.е. возврат)) в формате pdf
# pdf файлы(квитанции) скачиваются с сайта tax.gov.ua другим обработчиком.
# извлекает из имени(!) pdf файла уникальный (code, kvt) код и вид квитанции (какой отказ 2 или 3, или 4)
# по этим данным находит в базе данных данные о регистрации (reg_date, reg_number, receipt_status),
# чтобы определить к какой НН или РК относится квитанция.
# из содержимого файла (НЕ имени файла) извлекает дату и номер квитанции (receipt_date, receipt_number)
# затем все данные сохраняет в базу данных в другую таблицу.

import asyncio
import os
from datetime import datetime
from pathlib import Path

from AsyncPostgresql import get_result_one_row, async_save_pg
from Config import change_value_to_datetime
from ReadPDFByPytesseractAsync import extract_receipt_date_and_number_async
from tables.t_tax_cabinet_erpn_api_block import SQL_UPDATE_RECEIPT_DATA

cur_dir = os.path.dirname(os.path.abspath(__file__))
download_dir = os.path.join(cur_dir, "downloads", "pdf", "erpn_code")

wrong_files = []


async def extract_filename_from_path(path_pdf):
    arr = Path(path_pdf).stem.split()
    code = arr[0]
    kvt = None
    if code.isdigit():
        code = int(code)
    else:
        code = None

    if len(arr) == 1:
        kvt = 999
        return code, kvt
    elif len(arr) == 2:
        kvt = arr[1]
        if kvt.isdigit():
            kvt = int(kvt)
        else:
            kvt = None
    return code, kvt


async def move_pdf_to_another_folder(pdf_file):
    archive_dir = os.path.join(download_dir, "archive")
    os.makedirs(archive_dir, exist_ok=True)
    new_pdf_file = os.path.join(archive_dir, os.path.basename(pdf_file))
    os.rename(pdf_file, new_pdf_file)
    return new_pdf_file


async def extract_files_from_path():
    # Получаем список всех PDF файлов, которые заканчиваются на " 2.pdf", " 3.pdf" или " 4.pdf"
    extention = [".pdf"]
    pdf_files = []
    for ext in extention:
        # pdf_files += list(Path(download_dir).rglob(f"*{ext}"))  # рекурсивный поиск файлов
        pdf_files += list(Path(download_dir).glob(f"*{ext}"))  # поиск файлов в текущей папке
    return pdf_files


async def get_data_from_database(code, kvt):
    sql = """
        WITH erpn AS (
            SELECT DISTINCT
                erpn.code,
                erpn.ijcode,
                erpn.docrnn AS reg_number,
                erpn.impdate AS reg_date,
                erpn.hsmcsttname AS receipt_status,
                999 AS kvt_number,  -- используем только для документов НН и РК
                erpn.hsmcstt
            FROM t_tax_cabinet_erpn_api AS erpn
            UNION ALL 
            SELECT DISTINCT
                erpn.code,
                erpn.ijcode,
                erpn.docrnn AS reg_number,
                erpn.impdate AS reg_date,
                erpn.hsmcsttname AS receipt_status,
                0 AS kvt_number,  -- используем только для квитанций №1
                erpn.hsmcstt
            FROM t_tax_cabinet_erpn_api AS erpn
            UNION ALL 
            SELECT DISTINCT
                erpn.code,
                erpn.ijcode,
                erpn.docrnn AS reg_number,
                erpn.impdate AS reg_date,
                erpn.hsmcsttname AS receipt_status,
                CASE 
                    WHEN erpn.kvt2 = 1 THEN 2
                    ELSE NULL 
                END AS kvt_number,
                erpn.hsmcstt
            FROM t_tax_cabinet_erpn_api AS erpn
            UNION ALL 
            SELECT DISTINCT
                erpn.code,
                erpn.ijcode,
                erpn.docrnn AS reg_number,
                erpn.impdate AS reg_date,
                erpn.hsmcsttname AS receipt_status,
                CASE 
                    WHEN erpn.kvt3 = 1 THEN 3
                    ELSE NULL 
                END AS kvt_number,
                erpn.hsmcstt
            FROM t_tax_cabinet_erpn_api AS erpn
            UNION ALL 
            SELECT DISTINCT
                erpn.code,
                erpn.ijcode,
                erpn.docrnn AS reg_number,
                erpn.impdate AS reg_date,
                erpn.hsmcsttname AS receipt_status,
                CASE 
                    WHEN erpn.kvt4 = 1 THEN 4
                    ELSE NULL 
                END AS kvt_number,
                erpn.hsmcstt
            FROM t_tax_cabinet_erpn_api AS erpn
        )
        SELECT DISTINCT 
            erpn.code,
            erpn.ijcode,
            erpn.reg_number,
            erpn.reg_date,
            erpn.receipt_status,
            erpn.kvt_number
        FROM erpn
        WHERE code = $1::int8 AND kvt_number = $2::int 
        """

    result = await get_result_one_row(sql, code, kvt)
    return result


async def add_data_to_database(df, kvt_number=None, receipt_date=None, receipt_number=None):
    for index, row in df.iterrows():
        code = row.get("code")
        receipt_date = change_value_to_datetime(receipt_date)
        data = (
            receipt_date,
            receipt_number,
            int(code),
            int(kvt_number)
        )
        return await async_save_pg(SQL_UPDATE_RECEIPT_DATA, [data])


async def async_read_pdf():
    os.makedirs(download_dir, exist_ok=True)
    pdf_files = await extract_files_from_path()
    for pdf_file in pdf_files:
        receipt_date = None
        receipt_number = None
        code, kvt = await extract_filename_from_path(pdf_file)

        if not code or not kvt:
            # print(f"Ошибка в имени файла: {pdf_file}")
            wrong_files.append(pdf_file)
            continue

        # док НН/РК и квитанции №1 не читаем, сразу переносим в папку архив
        if code and kvt in [1, 999]:
            await move_pdf_to_another_folder(pdf_file)
            continue

        df = await get_data_from_database(code, kvt)
        receipt_date, receipt_number = await extract_receipt_date_and_number_async(pdf_file)
        if not receipt_date or not receipt_number:
            # print(f"Ошибка в файле: {pdf_file}")
            wrong_files.append(pdf_file)
            continue

        result = await add_data_to_database(df, kvt, receipt_date, receipt_number)
        if result:
            await move_pdf_to_another_folder(pdf_file)
        print(f"{result}, {pdf_file}")


if __name__ == "__main__":
    print("Start", datetime.now())
    asyncio.run(async_read_pdf())
    print("Wrong files:", wrong_files)
    print("End", datetime.now())
