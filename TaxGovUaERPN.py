# Description: Скрипт для автоматизации работы с сайтом tax.gov.ua
# сохраняет реестр в формате Excel с сайта по НН и РК

import asyncio
import os
import subprocess
import sys
import time
from datetime import datetime, timedelta, date
from pathlib import Path

import pyautogui
import pyperclip
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from ChangeKeyBoard import set_keyboard_layout
from TaxGovUaConfig import (
    authorizations,
    click_element_by_xpath,
    get_input_value,
    go_to_next_page,
    save_receipts,
    close_window_save_as,
    get_table_data,
    go_to_page,
    get_table_data_all, refresh_screen,
)

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

cur_dir = os.path.dirname(os.path.abspath(__file__))
download_dir = os.path.join(cur_dir, "downloads", "excel")
os.makedirs(download_dir, exist_ok=True)

prefs = {
    "download.prompt_for_download": True,  # Всегда указывать место для скачивания
    "download.directory_upgrade": True, # обновлять существующие директории загрузок.
    "download.default_directory": download_dir,  # Путь для загрузки файлов
}
chrome_options.add_experimental_option("prefs", prefs)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver_erpn = webdriver.Chrome(service=service, options=chrome_options)

# Максимизируем окно браузера
driver_erpn.maximize_window()


def open_window_for_save_file(file_path, count=1):
    try:
        # Если файл существует, удаляем его
        if os.path.exists(file_path):
            os.remove(file_path)

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Ожидаем появления и кликабельности кнопки "Експорт в Excel"
        export_button_xpath = (
            "//span[@class='p-button-label' and text()='Експорт в Excel']"
        )
        click_element_by_xpath(driver_erpn, export_button_xpath)
        time.sleep(3 * count)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        # pyautogui.write(pyperclip.paste())
        time.sleep(1 * count)
        # pyautogui.write(file_path)  # некорректно вводит путь к файлу. заменяет символы на другие
        pyautogui.press("enter")
        time.sleep(1)
        print(f"Файл {file_path} загружен")
        return file_path

    except Exception as e:
        return None


# Находим кнопку "Експорт в Excel" и сохраняем файл
def save_file(file_path):
    # Если файл существует, удаляем его
    if os.path.exists(file_path):
        os.remove(file_path)

    button_xpath = "//span[@class='p-button-label' and text()='Експорт в Excel']"
    save_receipts(driver_erpn, file_path, button_xpath)


# загружает данные в формате Excel с сайта tax.gov.ua по НН и РК
# и сохраняет их в базу данных
def load_taxdoc(date_first, date_last, date_doc_first=None, date_doc_last=None):
    try:
        # Открываем сайт
        driver_erpn.get("https://cabinet.tax.gov.ua/tax-invoices/written")
        refresh_screen(driver_erpn)
        date_input_first = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[1]/div/div/div[1]/p-calendar/span/input'
        date_input_second = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[2]/div/div/div[1]/p-calendar/span/input'

        date_out_first = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[1]/div/div/div[2]/p-calendar/span/input'
        date_out_second = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[2]/div/div/div[2]/p-calendar/span/input'
        # сначала удаляем все значения в поля начальной даты. Иначе возникает ошибка

        while True:
            date_input = WebDriverWait(driver_erpn, 3).until(
                EC.element_to_be_clickable((By.XPATH, date_input_first))
            )
            date_input.send_keys(Keys.CONTROL + "a")
            date_input.send_keys(Keys.DELETE)

            date_input = WebDriverWait(driver_erpn, 3).until(
                EC.element_to_be_clickable((By.XPATH, date_out_first))
            )
            date_input.send_keys(Keys.CONTROL + "a")
            date_input.send_keys(Keys.DELETE)
            date_input.send_keys(date_last)
            value_from_input_po = get_input_value(driver_erpn, date_out_first)

            date_input = WebDriverWait(driver_erpn, 3).until(EC.element_to_be_clickable((By.XPATH, date_input_first)))
            date_input.send_keys(Keys.CONTROL + "a")
            date_input.send_keys(Keys.DELETE)
            date_input.send_keys(date_first)
            value_from_input_z = get_input_value(driver_erpn, date_input_first)
            if parser.parse(value_from_input_z, dayfirst=True) and parser.parse(value_from_input_po, dayfirst=True):
                break

        if date_doc_first:
            while True:
                date_input = WebDriverWait(driver_erpn, 3).until(EC.element_to_be_clickable((By.XPATH, date_input_second)))
                date_input.send_keys(Keys.CONTROL + "a")
                date_input.send_keys(Keys.DELETE)
                date_input.send_keys(date_doc_first)
                value_from_input_in = get_input_value(driver_erpn, date_input_second)

                date_input = WebDriverWait(driver_erpn, 3).until(EC.element_to_be_clickable((By.XPATH, date_out_second)))
                date_input.send_keys(Keys.CONTROL + "a")
                date_input.send_keys(Keys.DELETE)
                date_input.send_keys(date_doc_last)
                value_from_input_po = get_input_value(driver_erpn, date_out_second)
                if parser.parse(value_from_input_in, dayfirst=True) and parser.parse(value_from_input_po, dayfirst=True):
                    break

        # Находим кнопку "Пошук" и кликаем по ней.
        # Загружаем данные согласно выставленным датам
        click_element_by_xpath(driver_erpn, "//span[text()='Пошук']")

        # Ожидаем загрузки страницы с таблицей
        time.sleep(5)

        page_xpath = '//*[@id="centerContent"]/app-ti-written/div[2]/p-table/div/p-paginator/div/button[4]/span'
        click_element_by_xpath(driver_erpn, page_xpath)
        time.sleep(5)

        max_page, max_page_count = go_to_next_page(driver_erpn, page_xpath)
        return max_page_count

    except Exception as e:
        pass
        # print(e)

    return False


def decode_output(output):
    # Попробуем декодировать с помощью нескольких кодировок
    for enc in ["utf-8", "windows-1251"]:
        try:
            return output.decode(enc)
        except UnicodeDecodeError:
            continue
    raise ValueError(
        "Не удалось декодировать вывод с использованием доступных кодировок."
    )


def add_excel_to_db():
    # Запускаем скрипт для чтения файлов Excel и записи в базу данных
    python_executable = sys.executable  # путь к python.exe из виртуального окружения
    script_path = os.path.join(os.path.dirname(__file__), "ReadXLS.py")

    try:
        result = subprocess.run(
            [python_executable, script_path],
            capture_output=True,
            text=False,  # Устанавливаем text=False для получения байтового вывода
            check=True,
        )

        # Декодируем вывод
        output = decode_output(result.stdout)
        print("Return code:", result.returncode)
        print("Output:", output)
    except subprocess.CalledProcessError as e:
        print("Ошибка выполнения скрипта:")
        print("Return code:", e.returncode)
        print("Output:", e.output)
        print("Error:", e.stderr)
    except ValueError as e:
        print(e)


async def cycle_month(date_first, date_last):
    if isinstance(date_first, datetime):
        date_first = date_first.date()
    elif isinstance(date_first, date):
        date_first = date_first
    else:
        date_first = parser.parse(date_first).date()

    if isinstance(date_last, datetime):
        date_last = date_last.date()
    elif isinstance(date_last, date):
        date_last = date_last
    else:
        date_last = parser.parse(date_last).date()

    # Авторизация
    authorizations(driver_erpn)

    while date_first < date_last:
        pyautogui.press("escape")
        driver_erpn.refresh()
        pyautogui.press("escape")
        await asyncio.sleep(1)  # Задержка для предотвращения перегрузки процессора

        # date_last = date_first + relativedelta(months=1) - timedelta(days=1)
        date_next = date_first + timedelta(days=5)
        if date_next > date_last:
            date_next = date_last
        date_one = date_first.strftime("%d.%m.%Y")
        date_two = date_next.strftime("%d.%m.%Y")
        page_count = load_taxdoc(date_one, date_two)
        control_page_count_and_save(page_count, date_first, date_next)
        # page_count = load_taxdoc("31.05.2022", "31.05.2022")
        # control_page_count_and_save(page_count, datetime.strptime("31.05.2022", "%d.%m.%Y"), datetime.strptime("31.05.2022", "%d.%m.%Y"))

        # в следующий раз начинаем с даты следующего дня
        date_first = date_next + timedelta(days=1)

    add_excel_to_db()


def save(date_one, date_two, doc_date_one=None, doc_date_two=None, count=1):
    date_one = parser.parse(date_one, dayfirst=True).strftime("%Y-%m-%d")
    date_two = parser.parse(date_two, dayfirst=True).strftime("%Y-%m-%d")
    if doc_date_one:
        doc_date_one = parser.parse(doc_date_one, dayfirst=True).strftime("%Y-%m-%d")
        doc_date_two = parser.parse(doc_date_two, dayfirst=True).strftime("%Y-%m-%d")
        file_path = os.path.join(
            cur_dir, "downloads", "excel", f"{date_one} {date_two} {doc_date_one} {doc_date_two}.xls"
        )
    else:
        file_path = os.path.join(
            cur_dir, "downloads", "excel", f"{date_one} {date_two}.xls"
        )

    # Сохраняем файл
    save_to = open_window_for_save_file(file_path, count + 1)
    return save_to


def control_page_count_and_save(max_page_count, date_first, date_last, count=5):
    # date_first, date_last - даты начала и окончания периода регистрации. Не период документа составления НН/РК

    # первоначальное значение = 6мес. Не период регистрации(!!!), а период когда сформирована НН/РК
    # Если документов больше 1000, то уменьшаем по циклу на 1 месяц
    delta_month = 6
    date_reg_one = date_first.strftime("%d.%m.%Y")  # дата начала периода регистрации
    date_reg_two = date_last.strftime("%d.%m.%Y")  # дата окончания периода регистрации

    # в Excel выгружается не болеее 1000 документов
    # табличная часть сайта содержит 10 док-в на странице
    # следовательно, максимальное кол-во страниц = 1000 / 10 = 100
    doc_date_one = date_first - relativedelta(years=1)
    if max_page_count > 99:
        while doc_date_one.date() <= date_last.date():
            print(f"кол-во страниц > 90: {doc_date_one.date()}, {date_last}, {max_page_count}")
            date_next = doc_date_one + relativedelta(months=delta_month)
            if date_next > date_last:
                date_next = date_last
            doc_date_one = doc_date_one.strftime("%d.%m.%Y")
            doc_date_two = date_next.strftime("%d.%m.%Y")
            max_page_count = load_taxdoc(date_reg_one, date_reg_two, doc_date_one, doc_date_two)
            if max_page_count <= 90:
                file_path = save(date_reg_one, date_reg_two, doc_date_one, doc_date_two)
                if not file_path:

                    # Прверяем существование файла. Если не существует, то повторяем попытку 5 раза
                    if not os.path.exists(file_path):
                        close_window_save_as(driver_erpn)
                        if count <= 5:
                            count += 1
                            time.sleep(5 * count)
                            load_taxdoc(date_reg_one, date_reg_two, doc_date_one, doc_date_two)  # рекурсивный вызов
                        else:
                            print(f"Файл {file_path} не загружен")
                            return False
                    else:
                        return True

                # в следующий раз начинаем с даты следующего дня
                doc_date_one = date_next + timedelta(days=1)
            else:  # если документов больше 990 (99 страниц), то уменьшаем период на 1 месяц
                doc_date_one = datetime.strptime(doc_date_one, "%d.%m.%Y")
                delta_month -= 1
    else:
        save(date_reg_one, date_reg_two)




if __name__ == "__main__":
    date_last = datetime.today()
    date_first = (date_last - relativedelta(months=1)).date()
    asyncio.run(cycle_month(date_first, date_last))
    driver_erpn.quit()
