# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1413203"
SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
      id uuid DEFAULT uuid_generate_v4() NOT NULL,
      hdate date NULL, -- Дата Повідомлення
      hnum varchar(30) NULL, -- Номер Повідомлення
      hnumreg int8 NULL, -- Номер реєстрації
      hdate1 date NULL, -- Дата реєстрації
      hnum2 varchar(30) NULL, -- Номер оскаржуваного рішення
      hdate2 date NULL, -- Дата оскаржуваного рішення
      r01g1d date NULL, -- Дата складання
      r01g21 int8 NULL, -- Номер ПН
      r01g22 varchar(10) NULL, -- Номер РК
      r01g3s varchar(35) NULL, -- Тип Документа
      r01g4 numeric(15, 2) NULL, -- Сума с ПДВ
      r01g5 numeric(15, 2) NULL, -- ПДВ
      r02g1s int8 NULL, -- ОКПО продавця
      r02g2 int8 NULL, -- ИНН продавця
      r02g3s varchar(100) NULL, -- Назва продавця
      r02g4d date NULL, -- Дата реєстрації ПДВ
      r02g5d date NULL, -- Дата припинення реєстрації ПДВ
      r03g1s int8 NULL, -- ОКПО покупця
      r03g2 numeric NULL, -- ИНН покупця
      r03g3s varchar(100) NULL, -- Назва покупця
      r03g4d date NULL, -- Дата реєстрації ПДВ
      r03g5d date NULL, -- Дата припинення реєстрації ПДВ
      m01 numeric(1) NULL, -- задоволено  скаргу
      m02 numeric(1) NULL, -- залишено скаргу без задоволення
      t1rxxxxg1s varchar NULL, -- Підстава
      hexecutor varchar(50) NULL, -- голова комісії
      filename varchar(55) NULL,
      CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 3. РІШЕННЯ за результатами розгляду скарги щодо рішення про відмову у реєстрації податкової накладної/розрахунку коригування';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnumreg IS 'Номер реєстрації';
    COMMENT ON COLUMN {TABLE_NAME}.hdate1 IS 'Дата реєстрації';
    COMMENT ON COLUMN {TABLE_NAME}.hnum2 IS 'Номер оскаржуваного рішення';
    COMMENT ON COLUMN {TABLE_NAME}.hdate2 IS 'Дата оскаржуваного рішення';
    COMMENT ON COLUMN {TABLE_NAME}.r01g1d IS 'Дата складання';
    COMMENT ON COLUMN {TABLE_NAME}.r01g21 IS 'Номер ПН';
    COMMENT ON COLUMN {TABLE_NAME}.r01g22 IS 'Номер РК';
    COMMENT ON COLUMN {TABLE_NAME}.r01g3s IS 'Тип Документа';
    COMMENT ON COLUMN {TABLE_NAME}.r01g4 IS 'Сума с ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r01g5 IS 'ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1s IS 'ОКПО продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2 IS 'ИНН продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g3s IS 'Назва продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g4d IS 'Дата реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r02g5d IS 'Дата припинення реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r03g1s IS 'ОКПО покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g2 IS 'ИНН покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g3s IS 'Назва покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g4d IS 'Дата реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r03g5d IS 'Дата припинення реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.m01 IS 'задоволено  скаргу';
    COMMENT ON COLUMN {TABLE_NAME}.m02 IS 'залишено скаргу без задоволення';
    COMMENT ON COLUMN {TABLE_NAME}.t1rxxxxg1s IS 'Підстава';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';
  
"""


async def main_t_tax_cabinet_j1413203_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1413203_async())
