# данные из ЕРПН через API о НН/РК
# асинхронный вариант

import datetime
import os
import time
from pathlib import Path
from AsyncPostgresql import CONN_PARAMS, async_save_pg
import pandas as pd
import aiohttp
import asyncio
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

import asyncpg
from ChangeKeyBoard import set_keyboard_layout
from ScrapeWithLogs import get_bearer_token
from TaxGovUaConfig import authorizations, refresh_screen
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

download_dir = os.path.join(cur_dir, "downloads", "pdf")
os.makedirs(download_dir, exist_ok=True)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

wrong_urls = []

# Авторизация на сайте
authorizations(driver)


def get_date_from_date_format(date):
    return parser.parse(date).date()


async def remove_duplicates(conn):
    sql = "SELECT fn_remove_duplicates('t_tax_cabinet_erpn_api');"
    await conn.execute(sql)


async def get_token(count=1):
    refresh_screen(driver)
    token = get_bearer_token(driver)
    if not token:
        await asyncio.sleep(10 * count)
        count += 1
        return await get_token(count)

    return token


async def get_erpn_data(session, date_from, date_to, token, page_number=0, retries=5):
    if not page_number:
        page_number = 0
    url = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="
    url = url + f"{date_from}%2000:00:00&toImpdate={date_to}%2023:59:59&page={page_number}&sort=impdate"
    headers = {'Authorization': token, "Content-Type": "application/json"}

    for attempt in range(retries):
        try:
            async with session.get(url, headers=headers, timeout=10) as result:
                if result.status == 200:
                    return await result.json()
                else:
                    print(f"Ошибка при получении данных из url: {url}")
                    pass
        except Exception as e:
            print(f"Неизвестная ошибка: {url}")
            pass

        await asyncio.sleep(5 * (1 + attempt))  # Ожидание перед повторной попыткой
        token = await get_token()  # Получаем новый токен
        headers['Authorization'] = token  # Обновляем заголовок с новым токеном
    wrong_urls.append(url)
    return None


async def parse_and_save_date(session, date_from, date_to, token, page_count, pool):
    for page_number in range(page_count):
        page_num, df = await get_pages_and_df(session, date_from, date_to, token, page_number)
        while True:
            try:
                df = df.apply(lambda x: x.fillna(0) if x.dtype.kind in 'biufc' else x)

                # Преобразование DataFrame в список кортежей для вставки
                records = df.to_dict(orient='records')
                columns = df.columns.tolist()
                values = [tuple(record[col] for col in columns) for record in records]

                # Формирование запроса для вставки данных
                insert_query = f"""
                INSERT INTO t_tax_cabinet_erpn_api ({', '.join(columns)})
                VALUES ({', '.join(['$' + str(i + 1) for i in range(len(columns))])})
                ON CONFLICT (code, ijcode)
                DO UPDATE SET
                    prdinn = EXCLUDED.prdinn,
                    pkpinn = EXCLUDED.pkpinn,
                    nmr = EXCLUDED.nmr,
                    crtdate = EXCLUDED.crtdate,
                    ndssm = EXCLUDED.ndssm,
                    r4100g11 = EXCLUDED.r4100g11,
                    tin = EXCLUDED.tin,
                    cptin = EXCLUDED.cptin,
                    crcode = EXCLUDED.crcode,
                    corrnmr = EXCLUDED.corrnmr,
                    flags = EXCLUDED.flags,
                    dend = EXCLUDED.dend,
                    ftype = EXCLUDED.ftype,
                    htypr = EXCLUDED.htypr,
                    waitcorr = EXCLUDED.waitcorr,
                    waitrisk = EXCLUDED.waitrisk,
                    impdate = EXCLUDED.impdate,
                    hsmcstt = EXCLUDED.hsmcstt,
                    hsmcsttname = EXCLUDED.hsmcsttname,
                    docrnn = EXCLUDED.docrnn,
                    hnamesel = EXCLUDED.hnamesel,
                    hnamebuy = EXCLUDED.hnamebuy,
                    kvt2 = EXCLUDED.kvt2,
                    kvt3 = EXCLUDED.kvt3,
                    kvt4 = EXCLUDED.kvt4,
                    id = EXCLUDED.id
                """

                # result = await async_save_pg(insert_query, values)
                async with pool.acquire() as conn:
                    await conn.executemany(insert_query, values)
                    print(f"Сохранено {date_from} {date_to}, стр {page_number + 1}/{page_count}")
                break
            except Exception as e:
                print(f"Ошибка при сохранении данных в базу данных: {e}")
                await asyncio.sleep(5)


async def get_pages_and_df(session, date_from, date_to, token, page_number=None):
    pages_count = 0
    df = pd.DataFrame()
    while True:
        if not token:
            token = await get_token()
        result = await get_erpn_data(session, date_from, date_to, token, page_number)
        if isinstance(result, dict) and not result.get("content"):
            df = pd.DataFrame(result.get("content"))
        elif isinstance(result, dict) and result.get("content"):
            pages_count = int(result.get("totalPages"))
            df = pd.DataFrame(result.get("content"))

            df["crtdate"] = pd.to_datetime(df["crtdate"])
            df["impdate"] = pd.to_datetime(df["impdate"])
            df["dend"] = pd.to_datetime(df["dend"])
            df["kvt2"] = df["kvt2"].astype(int)
            df["kvt3"] = df["kvt3"].astype(int)
            df["kvt4"] = df["kvt4"].astype(int)
            df["nmr"] = pd.to_numeric(df["nmr"], errors='coerce').fillna(0).astype(int)
            df["corrnmr"] = pd.to_numeric(df["corrnmr"], errors='coerce').fillna(0).astype(int)

            df.columns = [col.lower() for col in df.columns]

        if page_number is None:
            return pages_count, df
        else:
            return page_number, df


async def all_erpn_docs_api(date_from, date_to):
    date_from = get_date_from_date_format(date_from)
    date_to = get_date_from_date_format(date_to)
    token = await get_token()
    print(token)
    pool = await asyncpg.create_pool(**CONN_PARAMS)
    async with aiohttp.ClientSession() as session:
        while date_from <= date_to:
            date_current = date_from.strftime("%Y-%m-%d")
            page_count, df = await get_pages_and_df(session, date_current, date_current, token)
            if page_count:
                await parse_and_save_date(session, date_from, date_to, token, page_count, pool)
            date_from += relativedelta(days=1)
    async with pool.acquire() as conn:
        await remove_duplicates(conn)
    await pool.close()
    await session.close()
    await async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API)


if __name__ == "__main__":
    print(f"Start at {time.ctime()}")
    date_to = datetime.datetime.now().strftime("%Y-%m-%d")
    date_from = (datetime.datetime.now() - relativedelta(months=1)).strftime("%Y-%m-%d")
    asyncio.run(all_erpn_docs_api(date_from, date_to))
    driver.quit()
    print(f"Wrong urls: {wrong_urls}")
    print(f"End at {time.ctime()}")
