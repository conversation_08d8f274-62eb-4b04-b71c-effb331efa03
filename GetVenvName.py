import sys
import os

import sys

# путь к python.exe из виртуального окружения
def get_python_executable_path():
    return sys.executable

current_dir = os.path.dirname(__file__)


python_path = get_python_executable_path()
print(f"Путь к python.exe: {python_path}")

def get_virtual_env_name():
    if hasattr(sys, 'real_prefix'):
        # For virtualenv
        return os.path.basename(sys.prefix)
    elif sys.base_prefix != sys.prefix:
        # For venv
        return os.path.basename(sys.prefix)
    else:
        return None

env_name = get_virtual_env_name()
if env_name:
    print(f"Virtual environment name: {env_name}")
else:
    print("No virtual environment detected.")