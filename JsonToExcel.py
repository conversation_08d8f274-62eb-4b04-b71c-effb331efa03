import pandas as pd
import json
from openpyxl import Workbook
import requests

C_LOGIN = "rasim"
C_PSW = "15021972"
from AsyncPostgresql import URL_CONST

# Создаем новый Excel файл
wb = Workbook()
ws = wb.active
ws.title = "Invoice"


def get_invoices_head(document_key):
    url = (
        f"{URL_CONST}Document_РеализацияТоваровУслуг"
        "?$format=json"
        "&$orderby=Date desc"
        f"&$filter=Ref_Key eq guid'{document_key}'"
        "&$expand=*"
    )
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    return response.json()


def get_invoices_table(document_key):
    url = (
        f"{URL_CONST}Document_РеализацияТоваровУслуг_Товары/"
        "?$format=json"
        "&$expand=*"
        f"&$filter=Ref_Key eq guid'{document_key}'"
    )
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    return response.json()


def create_head(header):
    # Добавляем шапку накладной
    header = head.get("value")[0]
    ws.append(["Номер накладной", header["Number"]])
    ws.append(["Дата", header["Date"]])
    ws.append(["Организация", header.get("Организация").get("Description")])
    ws.append(["Договор", header.get("ДоговорКонтрагента").get("Description")])
    ws.append(["Контрагент", header.get("Контрагент").get("Description")])
    ws.append(["Валюта", header.get("ВалютаДокумента").get("Description")])
    ws.append([])  # Пустая строка для разделения


def create_table(data):
    # Добавляем таблицу товаров
    ws.append(
        [
            "№",
            "Номенклатура",
            "Ед. изм.",
            "СерияНоменклатуры",
            "Количество",
            "Цена",
            "Сумма",
        ]
    )

    for item in data["Товары"]:
        ws.append(
            [
                item.get("LineNumber"),
                item.get("Номенклатура").get("Description"),
                item.get("ЕдиницаИзмерения").get("Description"),
                item.get("СерияНоменклатуры").get("Description"),
                item.get("Количество"),
                item.get("Цена"),
                item.get("Сумма")
            ]
        )


def create_excel(document_key):
    head = get_invoices_head(f"{document_key}")
    if not head:
        return


    # Сохраняем в Excel
    wb.save("invoice.xlsx")
