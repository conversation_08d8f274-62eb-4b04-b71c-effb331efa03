from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager


def get_page_count(driver, url):
    # Открываем страницу
    driver.get(url)

    # Ожидаем загрузки страницы с таблицей
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, '//*[@id="centerContent"]/app-ti-written/div[2]/p-table/div/p-paginator'))
    )

    # Найдите элемент, содержащий номер последней страницы
    try:
        last_page_element = driver.find_element(By.XPATH, '//*[@id="centerContent"]/app-ti-written/div[2]/p-table/div/p-paginator/div/span[last()]')
        last_page_number = int(last_page_element.text)
        print(f"Номер последней страницы: {last_page_number}")
        return last_page_number
    except Exception as e:
        print(f"Ошибка при поиске номера последней страницы: {e}")
