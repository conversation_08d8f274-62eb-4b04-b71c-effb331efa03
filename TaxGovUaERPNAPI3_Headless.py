import asyncio
import os
import time
import json
from datetime import datetime

import aiohttp
import requests  # вначале установите библиотеку grequests, потом requests
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta

from AsyncPostgresql import async_save_pg
from TaxGovUaConfig import remove_duplicates
from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API

# Константы
url_const = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="

driver = None
token = None
wrong_urls = []


# Константы для портативного режима Chrome

def setup_portable_dirs():
    """
    Создает директории для портативного режима Chrome
    """

    cur_dir = os.path.dirname(os.path.abspath(__file__))

    # Создаем директории для портативного режима
    user_data_dir = os.path.join(cur_dir, "chrome_user_data")
    cache_dir = os.path.join(cur_dir, "chrome_cache")
    download_dir = os.path.join(cur_dir, "chrome_downloads")

    # Создаем директории, если они не существуют
    os.makedirs(user_data_dir, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)
    os.makedirs(download_dir, exist_ok=True)

    print(f"Созданы директории для портативного режима Chrome:")
    print(f"- Профиль: {user_data_dir}")
    print(f"- Кэш: {cache_dir}")
    print(f"- Загрузки: {download_dir}")

    return {
        "user_data_dir": user_data_dir,
        "cache_dir": cache_dir,
        "download_dir": download_dir
    }

def setup_chrome_options(headless=False):
    """
    Настраивает опции Chrome для работы в портативном режиме
    Параметр headless определяет, будет ли браузер запущен в фоновом режиме
    """
    from selenium.webdriver.chrome.options import Options

    # Получаем пути для портативного режима
    portable_dirs = setup_portable_dirs()

    # Настраиваем опции Chrome
    chrome_options = Options()

    # Добавляем опции для портативного режима
    chrome_options.add_argument(f"--user-data-dir={portable_dirs['user_data_dir']}")
    chrome_options.add_argument(f"--disk-cache-dir={portable_dirs['cache_dir']}")
    chrome_options.add_argument(f"--download.default_directory={portable_dirs['download_dir']}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-infobars")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-translate")

    # Добавляем опции для работы при заблокированном экране
    if headless:
        # Используем новый режим headless с маскировкой под обычный браузер
        chrome_options.add_argument("--headless=new")  # Новый режим headless
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--remote-debugging-port=9222")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        # Маскируем headless браузер под обычный
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)

        # Эмулируем обычные параметры браузера
        chrome_options.add_argument("--lang=uk-UA,uk;q=0.9,en-US;q=0.8,en;q=0.7")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")

        print("Браузер будет запущен в фоновом режиме (headless) с маскировкой под обычный браузер")
    else:
        chrome_options.add_argument("--start-maximized")

    # Добавляем опции для логирования сетевого трафика
    chrome_options.set_capability("goog:loggingPrefs", {"performance": "ALL"})

    print("Настроены опции Chrome для работы в портативном режиме")

    return chrome_options


def get_token(driver=None, token=None, count=1, headless=False):
    """
    Получает токен авторизации для API налоговой
    Использует Chrome в портативном режиме
    Параметр headless определяет, будет ли браузер запущен в фоновом режиме
    """
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import keyboard
    import pyautogui
    import pyperclip
    from dotenv import load_dotenv

    # Загружаем переменные окружения
    load_dotenv()

    # Настраиваем опции Chrome для портативного режима
    chrome_options = setup_chrome_options(headless=headless)

    # Очищаем куки и кэш, если драйвер уже существует
    if driver:
        driver.delete_all_cookies()
        driver.execute_script("window.localStorage.clear();")
        driver.execute_script("window.sessionStorage.clear();")

    while (not driver or not token) and count < 5:
        if not driver:
            try:
                if driver:
                    driver.quit()
            except Exception:
                pass

            try:
                print("Запускаем Chrome в портативном режиме...")
                # Запускаем браузер с настроенными опциями
                driver = webdriver.Chrome(options=chrome_options)
                print("Браузер Chrome успешно запущен в портативном режиме")
            except Exception as e:
                print(f"Ошибка при запуске Chrome: {e}")
                count += 1
                continue

            # Пытаемся авторизоваться
            if site_is_available(driver):
                print("Сайт налоговой доступен. Пытаемся авторизоваться...")
                authorize(driver, headless=headless)

        # Получаем токен
        token = get_bearer_token(driver, headless=headless)
        if token:
            print(f"\n\nТокен авторизации получен: {token}\n\n")
            return driver, token

        # Если токен не получен, возможно слетела авторизация
        if not driver:
            try:
                driver.quit()
                driver = None
            except Exception:
                pass

        time.sleep(5 * count)
        count += 1

    return None, None


def site_is_available(driver):
    """Проверяет доступность сайта налоговой"""
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    try:
        driver.get("https://cabinet.tax.gov.ua/login")
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "body"))
        )
        return True
    except Exception:
        return False


def authorize(driver=None, headless=False):
    """
    Авторизация на сайте налоговой
    Параметр headless определяет, работает ли браузер в фоновом режиме
    """
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import keyboard
    import pyautogui
    import pyperclip
    from dotenv import load_dotenv
    from ping3 import ping

    # Загружаем переменные окружения
    load_dotenv()

    # Всегда показываем режим авторизации
    print(f"Режим авторизации: {'headless (фоновый)' if headless else 'обычный'}")

    # Если работаем в фоновом режиме, то не используем pyautogui и keyboard
    # вместо этого используем прямой ввод через JavaScript и Selenium

    try:
        if ping("tax.gov.ua") is None:
            print("Сайт tax.gov.ua недоступен.")
            return False

        if not driver:
            return False

        # Максимизируем окно браузера
        driver.maximize_window()
        url_login = "https://cabinet.tax.gov.ua/login"

        # Открываем сайт
        driver.get(url_login)
        while not driver.current_url == url_login:
            refresh_screen(driver)
            driver.get(url_login)
            if not headless:
                pyautogui.hotkey("esc")
            time.sleep(1)

        # Ожидаем, пока блокирующий элемент исчезнет
        WebDriverWait(driver, 10).until(
            EC.invisibility_of_element_located((By.CLASS_NAME, "p-blockui-document"))
        )
        time.sleep(2)

        while True:
            if not headless:
                pyautogui.hotkey("esc")

            # Находим элемент dropdown и кликаем по нему, чтобы открыть список
            authorized_center_path = '//*[@id="selectedCAs111"]/div/span'

            # Ожидание элемента выпадающего списка с ID 'selectedCAs111' и клик по нему
            dropdown = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, authorized_center_path))
            )
            dropdown.click()
            click_element_by_xpath(
                driver, '//span[text()=\'КНЕДП ТОВ "Центр сертифікації ключів "Україна"\']'
            )
            pyautogui.hotkey("esc")
            time.sleep(2)

            # Вводим путь к файлу
            key_path = '//*[@id="keyStatusPanel"]/div/div[3]/div[2]/div/div/input'

            # Используем новый путь к файлу
            file_path = "d:\\Prestige\\Python\\Medoc\\41098985_2634316155_DU250326144705.ZS2"
            file_name = os.path.basename(file_path)  # Получаем только имя файла без пути
            print(f"Путь к файлу: {file_path}")
            print(f"Имя файла: {file_name}")

            # Находим поле ввода
            input_field = driver.find_element(By.XPATH, key_path)

            if headless:
                # В фоновом режиме используем JavaScript для прямого установления значения
                print("Устанавливаем значение поля через JavaScript")

                # Устанавливаем только имя файла без пути
                driver.execute_script(f"arguments[0].value = '{file_name}';", input_field)

                # Симулируем событие изменения
                driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", input_field)
                driver.execute_script("arguments[0].dispatchEvent(new Event('input'));", input_field)

                # Проверяем, что значение установлено
                value = input_field.get_attribute("value")
                print(f"Установленное значение поля: {value}")
            else:
                # В обычном режиме используем стандартный подход с диалоговым окном
                # Копируем путь к файлу в буфер обмена
                pyperclip.copy(file_path)
                print(f"Путь скопирован в буфер обмена: {pyperclip.paste()}")

                # Находим кнопку и кликаем по ней
                input_field.click()
                print("Кнопка выбора файла нажата")

                # Ждем появления диалогового окна
                time.sleep(2)

                # Вставляем путь из буфера обмена
                print("Вставляем путь из буфера обмена")
                keyboard.press_and_release('ctrl+v')
                time.sleep(1)

            # Нажимаем Enter для подтверждения
            print("Нажимаем Enter")
            if headless:
                # В фоновом режиме используем JavaScript для симуляции нажатия Enter
                driver.execute_script("arguments[0].dispatchEvent(new KeyboardEvent('keydown', {'key': 'Enter'}));", input_field)
                driver.execute_script("arguments[0].dispatchEvent(new KeyboardEvent('keyup', {'key': 'Enter'}));", input_field)
            else:
                pyautogui.press("enter")
            time.sleep(1)

            if not headless:
                pyautogui.hotkey("esc")

            # Находим поле для ввода пароля
            psw_path = '//*[@id="keyStatusPanel"]/div/div[3]/div[4]/div/div/input'
            print("Ищем поле для ввода пароля")
            password_input = driver.find_element(By.XPATH, psw_path)

            # Получаем пароль из переменной окружения
            password = os.getenv("TAX_GOV_UA_PASSWORD")
            print(f"Пароль из переменной окружения: {password if password else 'не установлен'}")
            # Если переменная окружения не установлена, используем фиксированный пароль
            if not password:
                password = "Prestige2023"  # Фиксированный пароль для тестирования

            if headless:
                # В фоновом режиме используем JavaScript для прямого установления значения
                print("Устанавливаем пароль через JavaScript")
                driver.execute_script(f"arguments[0].value = '{password}';", password_input)
                driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", password_input)
                driver.execute_script("arguments[0].dispatchEvent(new Event('input'));", password_input)
            else:
                # В обычном режиме используем стандартный подход
                print(f"Вводим пароль")
                password_input.send_keys(password)

            # Проверяем, что пароль введен
            value_psw = password_input.get_attribute("value")
            print(f"Пароль введен")

            # Проверяем значения полей
            value_key = get_input_value(driver, key_path)
            print(f"Значение поля файла: {value_key}")

            # Продолжаем выполнение
            break

        # Находим кнопку "Зчитати" и кликаем по ней
        read_button_xpath = "//span[text()='Зчитати']"
        read_button = driver.find_element(By.XPATH, read_button_xpath)

        print("Нажимаем кнопку 'Зчитати'")
        if headless:
            # В фоновом режиме используем JavaScript для клика
            driver.execute_script("arguments[0].click();", read_button)
        else:
            # В обычном режиме используем стандартный клик
            read_button.click()

        # Ожидаем пока прочитается файл
        print("Ожидаем пока прочитается файл...")
        time.sleep(5)

        # Находим кнопку "Увійти" и кликаем по ней
        login_button_xpath = "//span[text()='Увійти']"
        login_button = driver.find_element(By.XPATH, login_button_xpath)

        print("Нажимаем кнопку 'Увійти'")
        if headless:
            # В фоновом режиме используем JavaScript для клика
            driver.execute_script("arguments[0].click();", login_button)
        else:
            # В обычном режиме используем стандартный клик
            login_button.click()

        print("Авторизация успешно завершена")
        time.sleep(1)
        return True
    except Exception:
        pass

    return False


def refresh_screen(driver):
    """Обновляет страницу в браузере"""
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    try:
        driver.refresh()
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "body"))
        )
    except Exception:
        pass


def click_element_by_xpath(driver, xpath):
    """Клик по элементу с использованием XPath"""
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import (
        NoSuchElementException,
        StaleElementReferenceException,
        ElementClickInterceptedException,
        TimeoutException,
    )

    try:
        element = driver.find_element(By.XPATH, xpath)
        driver.execute_script("arguments[0].click();", element)
        # Ожидание появления и кликабельности элемента
        element = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, xpath)))

        # Скроллинг элемента в видимую область с помощью JavaScript
        driver.execute_script("arguments[0].scrollIntoView(true);", element)  # Прокручиваем элемент в видимую область
        element.click()

        # Ожидание нового состояния страницы
        WebDriverWait(driver, 1).until(
            EC.presence_of_element_located((By.XPATH, "//div[@class='new-content']"))
        )

    except ElementClickInterceptedException:
        time.sleep(1)  # Небольшая задержка для стабильности

        # Повторно находим элемент и кликаем на него с помощью JavaScript
        time.sleep(1)  # Небольшая задержка
        element = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, xpath)))
        driver.execute_script("arguments[0].click();", element)

    except (TimeoutException, NoSuchElementException, StaleElementReferenceException):
        pass
    except Exception:
        pass


def get_input_value(driver, xpath):
    """Получает значение поля ввода"""
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    try:
        input_element = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        return input_element.get_attribute("value")
    except Exception:
        return None


def get_bearer_token(driver, headless=False):
    """Получает Bearer токен из логов браузера или напрямую из API"""
    print("Получаем токен авторизации...")

    # Сначала пробуем получить токен из localStorage через JavaScript
    try:
        # Ждем, пока страница полностью загрузится
        driver.execute_script("return document.readyState") == "complete"

        # Переходим на страницу кабинета
        driver.get("https://cabinet.tax.gov.ua/")
        time.sleep(3)  # Даем время для загрузки страницы и выполнения JavaScript

        # Пробуем получить токен из localStorage через JavaScript
        oauth_token = driver.execute_script("""
            return localStorage.getItem('OAUTH_TOKEN') ||
                   localStorage.getItem('auth_token') ||
                   localStorage.getItem('token') ||
                   sessionStorage.getItem('OAUTH_TOKEN') ||
                   sessionStorage.getItem('auth_token') ||
                   sessionStorage.getItem('token');
        """)

        if oauth_token:
            print(f"Получен токен из хранилища: {oauth_token}")
            # Проверяем, является ли токен JSON-объектом
            try:
                token_obj = json.loads(oauth_token)
                if 'access_token' in token_obj:
                    return f"Bearer {token_obj['access_token']}"
            except:
                pass

            # Если токен не JSON или не содержит access_token
            if 'Bearer' not in oauth_token:
                return f"Bearer {oauth_token}"
            return oauth_token
    except Exception as e:
        print(f"Ошибка при получении токена из хранилища: {e}")

    # В режиме headless пробуем получить токен напрямую из API
    if headless:
        try:
            print("Пробуем получить токен напрямую из API...")

            # Делаем запрос к API налоговой
            test_url = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate=2023-01-01%2000:00:00&toImpdate=2023-01-01%2023:59:59&sort=impdate"

            # Используем сессию браузера для запроса
            print("Делаем запрос к API налоговой...")
            response = driver.execute_script("""
                var xhr = new XMLHttpRequest();
                xhr.open('GET', arguments[0], false);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.withCredentials = true;
                xhr.send();
                return {
                    status: xhr.status,
                    headers: xhr.getAllResponseHeaders(),
                    body: xhr.responseText
                };
            """, test_url)

            print(f"Ответ API: {response}")

            # Пробуем получить токен из локального хранилища
            local_storage = driver.execute_script("""
                var items = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    items[key] = localStorage.getItem(key);
                }
                return items;
            """)

            print(f"Локальное хранилище: {local_storage}")

            # Пробуем получить токен из сессионного хранилища
            session_storage = driver.execute_script("""
                var items = {};
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    items[key] = sessionStorage.getItem(key);
                }
                return items;
            """)

            print(f"Сессионное хранилище: {session_storage}")

            # Проверяем локальное хранилище на наличие токена
            for key, value in local_storage.items():
                if 'token' in key.lower() or 'auth' in key.lower():
                    print(f"Найден токен в локальном хранилище: {key} = {value}")
                    if 'Bearer' not in value:
                        return f"Bearer {value}"
                    return value

            # Проверяем сессионное хранилище на наличие токена
            for key, value in session_storage.items():
                if 'token' in key.lower() or 'auth' in key.lower():
                    print(f"Найден токен в сессионном хранилище: {key} = {value}")
                    if 'Bearer' not in value:
                        return f"Bearer {value}"
                    return value

            # Если не нашли токен в хранилищах, пробуем получить его из куки
            cookies = driver.get_cookies()
            for cookie in cookies:
                print(f"Cookie: {cookie['name']} = {cookie['value']}")
                if cookie['name'] == 'Authorization' or 'auth' in cookie['name'].lower() or 'token' in cookie['name'].lower():
                    print(f"Найден токен в куки: {cookie['name']} = {cookie['value']}")
                    if 'Bearer' not in cookie['value']:
                        return f"Bearer {cookie['value']}"
                    return cookie['value']
        except Exception as e:
            print(f"Ошибка при получении токена из API: {e}")

    # Пробуем получить токен через JavaScript на странице
    if headless:
        try:
            print("Пробуем получить токен через JavaScript на странице...")
            # Переходим на страницу кабинета
            driver.get("https://cabinet.tax.gov.ua/")
            time.sleep(5)  # Даем время для загрузки страницы

            # Выполняем JavaScript для получения токена
            token = driver.execute_script("""
                // Функция для перехвата XHR запросов
                var token = '';
                var originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function() {
                    this.addEventListener('load', function() {
                        try {
                            // Проверяем заголовки ответа
                            var authHeader = this.getResponseHeader('Authorization');
                            if (authHeader) {
                                token = authHeader;
                            }

                            // Проверяем тело ответа на наличие токена
                            var responseText = this.responseText;
                            if (responseText && responseText.includes('access_token')) {
                                try {
                                    var data = JSON.parse(responseText);
                                    if (data.access_token) {
                                        token = 'Bearer ' + data.access_token;
                                    }
                                } catch(e) {}
                            }
                        } catch(e) {}
                    });
                    originalOpen.apply(this, arguments);
                };

                // Делаем запрос к API
                var xhr = new XMLHttpRequest();
                xhr.open('GET', '/ws/api/nlnk/nlnkhd?fromImpdate=2023-01-01%2000:00:00&toImpdate=2023-01-01%2023:59:59&sort=impdate', false);
                xhr.withCredentials = true;
                try { xhr.send(); } catch(e) {}

                // Возвращаем токен
                return token || localStorage.getItem('OAUTH_TOKEN') || localStorage.getItem('auth_token');
            """)

            if token and len(token) > 10:  # Проверяем, что токен не пустой и имеет разумную длину
                print(f"Получен токен через JavaScript: {token}")
                if 'Bearer' not in token:
                    return f"Bearer {token}"
                return token
        except Exception as e:
            print(f"Ошибка при получении токена через JavaScript: {e}")

    # Получаем токен из логов браузера
    try:
        logs = driver.get_log("performance")
        print(f"Получено {len(logs)} записей логов")

        # Пробуем получить токен из логов
        token = get_token_from_logs(logs)
        if token:
            print(f"Токен получен из логов: {token}")
            return token

        # Пробуем получить токен из сетевых запросов
        network_logs = list(process_browser_logs_for_network_events(logs))
        print(f"Получено {len(network_logs)} сетевых записей")

        for log in network_logs:
            try:
                headers = log.get('params', {}).get('headers', {})
                auth = headers.get('Authorization')
                if auth is not None and auth != "Bearer undefined" and 'Bearer' in auth:
                    print(f"Токен получен из сетевых записей: {auth}")
                    return auth
            except Exception as e:
                print(f"Ошибка при обработке сетевой записи: {e}")
                continue

        # Пробуем найти токен в данных
        auth = find_authorization_dict(logs)
        if auth:
            print(f"Токен получен из данных: {auth}")
            return auth
    except Exception as e:
        print(f"Ошибка при получении токена: {e}")

    # Если не удалось получить токен, пробуем еще раз в том же режиме
    if headless:
        print("Не удалось получить токен в режиме headless. Пробуем еще раз...")

        # Закрываем текущий браузер
        driver.quit()

        # Запускаем браузер снова в режиме headless
        from selenium import webdriver
        chrome_options = setup_chrome_options(headless=True)
        new_driver = webdriver.Chrome(options=chrome_options)

        # Авторизуемся в режиме headless
        if site_is_available(new_driver):
            authorize(new_driver, headless=True)

            # Получаем токен в режиме headless
            token = get_bearer_token(new_driver, headless=True)
            if token:
                print(f"Токен получен в режиме headless: {token}")
                return token
            else:
                print("Не удалось получить токен в режиме headless")
                new_driver.quit()
                return None

    print("Не удалось получить токен авторизации")
    return None


def process_browser_logs_for_network_events(logs):
    """
    Обрабатывает логи браузера и возвращает только сетевые события
    """
    import json

    for entry in logs:
        log = json.loads(entry["message"])["message"]
        if (
                "Network.response" in log["method"]
                or "Network.request" in log["method"]
                or "Network.webSocket" in log["method"]
        ):
            yield log


def get_token_from_logs(logs):
    """Извлекает токен из логов браузера"""
    import re

    logs_str = str(logs)
    pattern = r'"Authorization":"Bearer\s(?!undefined)[\w-]+"'
    matches = re.findall(pattern, logs_str)
    if matches:
        return matches[-1].split(":")[-1].replace('"', '')
    return None


def find_authorization_dict(data):
    """Ищет токен авторизации в данных"""
    import json

    if not isinstance(data, (dict, list)):
        try:
            data = json.loads(data)
        except:
            return None

    if isinstance(data, dict):
        for key, value in data.items():
            if key == 'Authorization' and value != "Bearer undefined" and 'Bearer' in value:
                return data
            elif isinstance(value, (dict, list)):
                result = find_authorization_dict(value)
                if result:
                    return result
    elif isinstance(data, list):
        for item in data:
            result = find_authorization_dict(item)
            if result:
                return result
    return None


# Создаем url с датой начала и конца периода. Нужно для получения количества страниц
async def get_pages_urls(date_in, date_out):
    date_in = parse(date_in, dayfirst=True).date()
    date_out = parse(date_out, dayfirst=True).date()
    interval_days = (date_out - date_in).days
    return [
        (
            f"{url_const}{(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2000:00:00"
            f"&toImpdate={(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2023:59:59"
            "&sort=impdate"
        )
        for i in range(interval_days + 1)
    ]


# получение количества страниц в данном периоде с токеном
async def get_page_count_async_with_token(url, session, semaphore, token, retries=3):
    """Получение количества страниц в данном периоде с токеном"""
    global url_const, wrong_urls
    async with semaphore:
        await asyncio.sleep(5)
        for attempt in range(retries):
            print(f"{datetime.now()}; url: {url}")
            headers = {"Authorization": token, "Content-Type": "application/json"}
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        result = await response.json()
                        return [url, result.get("totalPages")]
            except aiohttp.ClientError as e:
                print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                # print(f"Тайм-аут при запросе данных: {url}")
                pass
            await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой

    wrong_urls.append(url)
    return [0, 0]

# получение количества страниц в данном периоде
async def get_page_count_async(url, session, semaphore, retries=3):
    global url_const, token, wrong_urls, driver
    async with semaphore:
        await asyncio.sleep(5)
        for attempt in range(retries):
            print(f"{datetime.now()}; url: {url}")
            if not token:
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token(headless=True)
                print(f"Update token: {datetime.now()}; {token}")
            headers = {"Authorization": token, "Content-Type": "application/json"}
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        result = await response.json()
                        return [url, result.get("totalPages")]
                    elif response and response.status == 401:  # Unauthorized - токен недействителен
                        print(f"Токен недействителен (401), получаем новый...")
                        if driver:
                            driver.quit()
                            driver = None
                        driver, token = get_token(headless=True)
                        print(f"Update token: {datetime.now()}; {token}")
                        # Обновляем заголовки с новым токеном
                        headers = {"Authorization": token, "Content-Type": "application/json"}
            except aiohttp.ClientError as e:
                print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                # print(f"Тайм-аут при запросе данных: {url}")
                pass
            await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой

    wrong_urls.append(url)
    return [0, 0]


async def get_page_count(url, retries=3):
    global url_const, driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token(headless=True)
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                return [url, result.get("totalPages")]
            elif response and response.status_code == 401:  # Unauthorized - токен недействителен
                print(f"Токен недействителен (401), получаем новый...")
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token(headless=True)
                print(f"Update token: {datetime.now()}; {token}")
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    print(f"ERROR page url: {url}\n{response.text}")
    wrong_urls.append(url)
    return [0, 0]


# Асинхронная функция для выполнения HTTP-запроса
async def fetch(url, retries=3):
    global driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token(headless=True)
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                return result
            elif response and response.status_code == 401:  # Unauthorized - токен недействителен
                print(f"Токен недействителен (401), получаем новый...")
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token(headless=True)
                print(f"Update token: {datetime.now()}; {token}")
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    # print(f"count wrong_urls: {len(wrong_urls)}; url: {url}")
    print(f"ERROR url: {url}\n{response.text}")
    wrong_urls.append(url)
    return None


# отбираем url с количеством страниц, у которых количество страниц больше 0
async def create_url_with_pages(urls_pages):
    return [[url, pages] for url, pages in urls_pages if pages > 0]


# Создаем url для периода с номером страницы.
# Количество url = количество страниц
async def create_urls(url_pages):
    all_urls = []
    for url, page_number in url_pages:
        urls = [f"{url}&page={page}" for page in range(page_number)]
        all_urls.extend(urls)
    return all_urls


# Асинхронная функция для выполнения HTTP-запроса с токеном
async def fetch_async_with_token(url, session, semaphore, token, retries=3):
    """Асинхронная функция для выполнения HTTP-запроса с токеном"""
    global wrong_urls
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        return await response.json()
            except Exception as e:
                print(f"Ошибка при запросе {url}: {e}")
                await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой

    wrong_urls.append(url)
    return None

# Асинхронная функция для выполнения HTTP-запроса
async def fetch_async(url, session, semaphore, retries=3):
    global driver, token, wrong_urls
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        return await response.json()
                    elif response and response.status == 401:  # Unauthorized - токен недействителен
                        print(f"Токен недействителен (401), получаем новый...")
                        if driver:
                            driver.quit()
                            driver = None
                        driver, token = get_token(headless=True)
                        print(f"Update token: {datetime.now()}; {token}")
                        # Обновляем заголовки с новым токеном
                        headers = {"Authorization": token, "Content-Type": "application/json"}
            except Exception as e:
                print(f"Ошибка при запросе {url}: {e}")
                await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой

    wrong_urls.append(url)
    return None


# Асинхронная функция для запуска задач по 5 штук одновременно
async def run_tasks(urls, pages=False):
    semaphore = asyncio.Semaphore(5)  # Ограничение на 5 одновременных задач
    async with aiohttp.ClientSession() as session:
        if not pages:
            # В режиме headless используем асинхронные запросы
            if driver is None and token:
                tasks = [fetch_async_with_token(url, session, semaphore, token) for url in urls]
            else:
                tasks = [fetch(url) for url in urls]
        else:
            # В режиме headless используем асинхронные запросы
            if driver is None and token:
                tasks = [get_page_count_async_with_token(url, session, semaphore, token) for url in urls]
            else:
                tasks = [get_page_count(url) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


# получаем content из json
async def validate_token(token):
    """Проверяет валидность токена и возвращает информацию о нем"""
    import aiohttp
    import base64
    import json

    # Делаем тестовый запрос к API налоговой
    test_url = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate=2023-01-01%2000:00:00&toImpdate=2023-01-01%2023:59:59&sort=impdate"
    headers = {"Authorization": token, "Content-Type": "application/json"}

    # Анализируем токен
    token_info = {}
    try:
        # Извлекаем часть токена без префикса Bearer
        token_value = token.replace("Bearer ", "")

        # Пробуем проанализировать токен как строку
        token_info['type'] = 'Bearer Token'
        token_info['length'] = len(token_value)

        # Проверяем, похож ли токен на JWT (имеет две точки)
        if token_value.count('.') == 2:
            try:
                # Разделяем токен на части
                parts = token_value.split('.')
                # Пробуем декодировать части
                try:
                    # Добавляем padding если необходимо
                    def add_padding(s):
                        # JWT base64url не использует padding, но стандартный base64 требует
                        # Добавляем padding '=' в конец, если длина не кратна 4
                        return s + '=' * (4 - len(s) % 4) if len(s) % 4 else s

                    # Декодируем заголовок
                    padded = add_padding(parts[0])
                    # Заменяем символы base64url на стандартные base64
                    padded = padded.replace('-', '+').replace('_', '/')
                    header_json = base64.b64decode(padded).decode('utf-8')
                    header = json.loads(header_json)

                    # Декодируем полезную нагрузку
                    padded = add_padding(parts[1])
                    padded = padded.replace('-', '+').replace('_', '/')
                    payload_json = base64.b64decode(padded).decode('utf-8')
                    payload = json.loads(payload_json)

                    token_info['type'] = 'JWT'
                    token_info['header'] = header
                    token_info['payload'] = payload

                    # Проверяем срок действия
                    if 'exp' in payload:
                        from datetime import datetime
                        exp_time = datetime.fromtimestamp(payload['exp'])
                        now = datetime.now()
                        token_info['expires_at'] = exp_time.strftime('%Y-%m-%d %H:%M:%S')
                        token_info['is_expired'] = exp_time < now
                        token_info['time_left'] = str(exp_time - now) if exp_time > now else "Истек"
                except Exception as e:
                    token_info['decode_error'] = str(e)
            except Exception as e:
                token_info['parse_error'] = str(e)
    except Exception as e:
        token_info['error'] = f"Ошибка при анализе токена: {e}"

    # Проверяем валидность токена через API
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(test_url, headers=headers, timeout=10) as response:
                token_info['api_status_code'] = response.status
                if response.status == 200:
                    print("Токен валиден для API")
                    token_info['is_valid'] = True
                    return True, token_info
                else:
                    print(f"Токен недействителен для API, код ответа: {response.status}")
                    token_info['is_valid'] = False
                    token_info['response_text'] = await response.text()
                    return False, token_info
    except Exception as e:
        print(f"Ошибка при проверке токена через API: {e}")
        token_info['is_valid'] = False
        token_info['api_error'] = str(e)
        return False, token_info


async def get_content_from_response(responses):
    contents = [r.get("content") for r in responses if r]
    return [data_dict.values() for data_list in contents for data_dict in data_list]


# Преобразование данных в нужный формат
async def convert_data(data):
    data[3] = int(data[3]) if data[3] else 0  # Преобразование nmr в int
    data[11] = int(data[11]) if data[11] else 0  # Преобразование corrnmr в int
    data[4] = parse(data[4]).date() if data[4] else data[4]
    data[13] = parse(data[13]).date() if data[13] else data[13]
    data[18] = parse(data[18]).date() if data[18] else data[18]
    return tuple(data)


async def main(date_from, date_to, headless=False, ignore_auth_errors=False, max_auth_attempts=3):
    global wrong_urls, driver, token

    # Не используем сохраненный токен
    print("Токены не сохраняются в файл для повышения безопасности")

    # Получаем новый токен только если он не существует
    if not token:
        driver, token = get_token(headless=headless)
        if not token:
            print("Не удалось получить токен авторизации")
            return

    # Выводим информацию о токене
    print(f"Токен авторизации получен: {token}")

    # Проверяем и выводим дополнительную информацию о токене
    try:
        is_valid, token_info = await validate_token(token)
        print(f"\nДетальная информация о токене:")
        print(f"- Валидность: {'Валиден' if is_valid else 'Недействителен'}")
        print(f"- Тип: {token_info.get('type', 'Неизвестно')}")

        if 'header' in token_info:
            print(f"- Алгоритм: {token_info['header'].get('alg', 'Неизвестно')}")

        if 'payload' in token_info:
            payload = token_info['payload']
            if 'sub' in payload:
                print(f"- Субъект: {payload['sub']}")
            if 'iss' in payload:
                print(f"- Издатель: {payload['iss']}")
            if 'iat' in payload:
                from datetime import datetime
                iat_time = datetime.fromtimestamp(payload['iat'])
                print(f"- Выдан: {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")

        if 'expires_at' in token_info:
            print(f"- Срок действия до: {token_info.get('expires_at')}")
            print(f"- Осталось времени: {token_info.get('time_left')}")

        print(f"- Статус API: {token_info.get('api_status_code')}")
    except Exception as e:
        print(f"Ошибка при получении информации о токене: {e}")

    urls = await get_pages_urls(date_from, date_to)
    urls_pages = await run_tasks(urls, pages=True)
    repeat_count = 1
    while len(wrong_urls) > 0:
        # Если игнорируем ошибки авторизации, то не обновляем токен
        if not ignore_auth_errors:
            # Получаем новый токен только если достигнут лимит попыток
            if repeat_count <= max_auth_attempts:
                # Получаем новый токен
                _, token = get_token(driver=driver, headless=headless)
                print(f"Update token: {datetime.now()}; {token}")
            else:
                print(f"Достигнуто максимальное количество попыток авторизации ({max_auth_attempts}). Продолжаем работу с текущим токеном.")

        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL страниц: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        urls_pages.extend(await run_tasks(urls, pages=True))

    urls_pages_greater_0 = await create_url_with_pages(urls_pages)
    urls = await create_urls(urls_pages_greater_0)
    responses = await run_tasks(urls)
    repeat_count = 1
    while len(wrong_urls) > 0:
        # Если игнорируем ошибки авторизации, то не обновляем токен
        if not ignore_auth_errors:
            # Получаем новый токен только если достигнут лимит попыток
            if repeat_count <= max_auth_attempts:
                # Получаем новый токен
                _, token = get_token(driver=driver, headless=headless)
                print(f"Update token: {datetime.now()}; {token}")
            else:
                print(f"Достигнуто максимальное количество попыток авторизации ({max_auth_attempts}). Продолжаем работу с текущим токеном.")

        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        responses.extend(await run_tasks(urls))

    # удаляем пустые значения из списка
    responses = [r for r in responses if r]
    datas = await get_content_from_response(responses)
    tasks = [convert_data(list(data)) for data in datas]
    data = await asyncio.gather(*tasks)
    result = await async_save_pg(SQL_INSERT_ERPN, data)
    print(f"Data saved: {result}")

    result = await async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API)
    print(f"Data saved to t_tax_cabinet_erpn_api_block: {result}")

    result = remove_duplicates("t_tax_cabinet_erpn_api")
    print(f"Remove duplicates: {result}")


if __name__ == "__main__":
    import argparse

    # Создаем парсер аргументов командной строки
    parser = argparse.ArgumentParser(description="Получение данных из API налоговой")
    parser.add_argument("--date-from", help="Дата начала периода (YYYY-MM-DD)")
    parser.add_argument("--date-to", help="Дата окончания периода (YYYY-MM-DD)")
    parser.add_argument("--headless", action="store_true", help="Запустить браузер в фоновом режиме")
    parser.add_argument("--ignore-auth-errors", action="store_true", help="Игнорировать ошибки авторизации и продолжать работу")
    parser.add_argument("--max-auth-attempts", type=int, default=3, help="Максимальное количество попыток авторизации")
    parser.add_argument("--token-info", action="store_true", help="Вывести подробную информацию о токене и выйти")

    args = parser.parse_args()

    print("Начало", datetime.now())

    # Если даты не указаны, используем значения по умолчанию
    date_to = args.date_to if args.date_to else datetime.now().strftime("%Y-%m-%d")
    date_from = args.date_from if args.date_from else (datetime.now() - relativedelta(months=4)).replace(day=1).strftime("%Y-%m-%d")
    ignore_auth_errors = args.ignore_auth_errors
    max_auth_attempts = args.max_auth_attempts
    token_info_only = args.token_info

    print(f"Период: с {date_from} по {date_to}")
    print(f"Режим браузера: {'headless (фоновый)' if args.headless else 'обычный'}")
    if ignore_auth_errors:
        print("Режим игнорирования ошибок авторизации: Включен")
    print(f"Максимальное количество попыток авторизации: {max_auth_attempts}")

    try:
        # Если указан параметр --token-info, то только получаем информацию о токене и выходим
        if token_info_only:
            # Получаем токен
            driver, token = get_token(headless=args.headless)
            if token:
                # Проверяем токен и выводим информацию
                is_valid, token_info = asyncio.run(validate_token(token))
                print(f"\nДетальная информация о токене:")
                print(f"- Валидность: {'Валиден' if is_valid else 'Недействителен'}")
                print(f"- Тип: {token_info.get('type', 'Неизвестно')}")
                print(f"- Длина: {token_info.get('length', 'Неизвестно')}")

                if 'header' in token_info:
                    print(f"- Алгоритм: {token_info['header'].get('alg', 'Неизвестно')}")

                if 'payload' in token_info:
                    payload = token_info['payload']
                    if 'sub' in payload:
                        print(f"- Субъект: {payload['sub']}")
                    if 'iss' in payload:
                        print(f"- Издатель: {payload['iss']}")
                    if 'iat' in payload:
                        from datetime import datetime
                        iat_time = datetime.fromtimestamp(payload['iat'])
                        print(f"- Выдан: {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")

                if 'expires_at' in token_info:
                    print(f"- Срок действия до: {token_info.get('expires_at')}")
                    print(f"- Осталось времени: {token_info.get('time_left')}")

                print(f"- Статус API: {token_info.get('api_status_code')}")

                # Не сохраняем токен в файл
                print("\nТокен не будет сохранен в файл для повышения безопасности")
            else:
                print("Не удалось получить токен")
        else:
            # Запускаем основной скрипт
            asyncio.run(main(date_from, date_to, headless=args.headless, ignore_auth_errors=ignore_auth_errors, max_auth_attempts=max_auth_attempts))

    except Exception as e:
        print(f"Ошибка при выполнении скрипта: {e}")
    finally:
        if driver:
            driver.quit()

    print("Конец", datetime.now())

