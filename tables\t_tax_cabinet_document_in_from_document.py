# сохраняются данные полученные из PDF/XML загруженные из https://cabinet.tax.gov.ua/documents/in
# тип документов Решения

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_document_in_from_document"
SQL_CREATE_TABLE = f"""
    -- DROP TABLE IF EXISTS {TABLE_NAME};
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL, -- Ідентифікатор
        resolutions_date date NULL, -- Дата решения
        resolutions_number varchar(20) NULL, -- Номер решения
        doc_date date NULL, -- Дата документа
        doc_number numeric(10) NULL, -- Номер документа
        doc_type varchar(20) NULL, -- Тип документа
        total_amount numeric(20, 2) NULL, -- Сумма
        tax_amount numeric(20, 2) NULL, -- НДС
        supplier_ipn varchar(215) NULL, -- ИПН поставщика
        supplier_okpo varchar(15) NULL, -- ОКПО поставщика
        supplier_name varchar(100) NULL, -- Название поставщика
        customer_ipn varchar(15) NULL, -- ИПН покупателя
        customer_okpo varchar(10) NULL, -- ОКПО покупателя
        customer_name varchar(100) NULL, -- Название покупателя
        description varchar NULL, -- Описание
        file_name varchar(200) NULL, -- Название файла
        reg_num numeric(15) NULL, -- номер регистрации
        reg_date date NULL, -- дата регистрации
        resolutions_date2 date NULL, -- Дата решения
        resolutions_number2 varchar(20) NULL, -- Номер решения
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (resolutions_number, doc_number)
);
  
    COMMENT ON TABLE {TABLE_NAME} IS 'инф о документах из PDF/XML загруженных из https://cabinet.tax.gov.ua/documents/in';
    COMMENT ON COLUMN public.{TABLE_NAME}.id IS 'Ідентифікатор';
    COMMENT ON COLUMN public.{TABLE_NAME}.resolutions_date IS 'Дата решения';
    COMMENT ON COLUMN public.{TABLE_NAME}.resolutions_number IS 'Номер решения';
    COMMENT ON COLUMN public.{TABLE_NAME}.doc_date IS 'Дата документа';
    COMMENT ON COLUMN public.{TABLE_NAME}.doc_number IS 'Номер документа';
    COMMENT ON COLUMN public.{TABLE_NAME}.doc_type IS 'Тип документа';
    COMMENT ON COLUMN public.{TABLE_NAME}.total_amount IS 'Сумма';
    COMMENT ON COLUMN public.{TABLE_NAME}.tax_amount IS 'НДС';
    COMMENT ON COLUMN public.{TABLE_NAME}.supplier_ipn IS 'ИПН поставщика';
    COMMENT ON COLUMN public.{TABLE_NAME}.supplier_okpo IS 'ОКПО поставщика';
    COMMENT ON COLUMN public.{TABLE_NAME}.supplier_name IS 'Название поставщика';
    COMMENT ON COLUMN public.{TABLE_NAME}.customer_ipn IS 'ИПН покупателя';
    COMMENT ON COLUMN public.{TABLE_NAME}.customer_okpo IS 'ОКПО покупателя';
    COMMENT ON COLUMN public.{TABLE_NAME}.customer_name IS 'Название покупателя';
    COMMENT ON COLUMN public.{TABLE_NAME}.description IS 'Описание';
    COMMENT ON COLUMN public.{TABLE_NAME}.file_name IS 'Название файла';
    COMMENT ON COLUMN public.{TABLE_NAME}.reg_num IS 'номер регистрации';
    COMMENT ON COLUMN public.{TABLE_NAME}.reg_date IS 'дата регистрации';
    COMMENT ON COLUMN public.{TABLE_NAME}.resolutions_date2 IS 'Дата решения';
    COMMENT ON COLUMN public.{TABLE_NAME}.resolutions_number2 IS 'Номер решения';
"""

SQL_INSERT_DOCUMENT_IN_FROM_DOCIN = f"""
  INSERT INTO {TABLE_NAME} 
  (    
    resolutions_date,
    resolutions_number,
    doc_date,
    doc_number,
    doc_type,
    total_amount,
    tax_amount,
    supplier_okpo,
    supplier_ipn,
    supplier_name,
    customer_okpo,
    customer_ipn,
    customer_name,
    description,
    file_name  
  )
  VALUES (
    $1,
    $2, 
    $3,
    $4, 
    $5, 
    $6, 
    $7, 
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14,
    $15
    )
    ON CONFLICT (resolutions_number, doc_number) DO NOTHING
  ;
"""


async def main_tax_cabinet():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_tax_cabinet())
