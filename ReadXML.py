# в xml файлах находится информация о решениях налоговых органов, которые были приняты по НН, РК
# данный скрипт циклом проходит по всем xml файлам в папке downloads/XML
# и сохраняет информацию в таблицу t_tax_cabinet_document_in_from_document

import os
import xml.etree.ElementTree as ET

import pandas as pd

from AsyncPostgresql import engine
from XMLtypes import find_type_by_columns, get_type_rest, get_type_columns
from tables.t_tax_cabinet_document_in_from_document import TABLE_NAME


def xml_to_dict(element):
    node = {}
    if element.text and element.text.strip():
        node['text'] = element.text.strip()

    for key, value in element.attrib.items():
        node[key] = value

    for child in element:
        child_dict = xml_to_dict(child)
        if child.tag not in node:
            node[child.tag] = child_dict
        else:
            if not isinstance(node[child.tag], list):
                node[child.tag] = [node[child.tag]]
            node[child.tag].append(child_dict)

    return node


def parse_xml(xml_file):
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # Преобразование XML в словарь (парсить удобнее)
        return xml_to_dict(root)
    except Exception as e:
        print(f"Error parsing xml file {xml_file}: {e}")
        return None


# выводим список xml файлов в папке
def get_files_by_ext(path, ext):
    xml_files = [f for f in os.listdir(path) if f.endswith(ext)]
    return xml_files


def df_column_to_date(df, column):
    # Преобразование столбца в целочисленный тип для удаления суффикса .0
    df[column] = df[column].astype(int).astype(str)

    # Проверка длины значения и добавление 0 спереди, если длина равна 7
    df[column] = df[column].apply(lambda x: '0' + x if len(x) == 7 else x)
    df.loc[:, column] = pd.to_datetime(df[column], format="%d%m%Y")

    return df


def df_column_to_str(df, column):
    df[column] = df[column].apply(lambda x: abs(int(x))).astype(str)
    return df


# Функция для переименования колонок и преобразования типов
def df_rename_columns(df, xml_type):
    with_columns = get_type_rest(xml_type)
    rename_dict = get_type_columns(xml_type)

    # Удаление ненужных колонок
    df = df.loc[:, with_columns]

    # Переименование колонок
    df.rename(columns=rename_dict, inplace=True)

    # Удаление первой строки
    df.drop(df.index[0], inplace=True)

    # Преобразование столбца в строковый тип и удаление суффикса .0
    df_column_to_date(df, "resolutions_date")
    df_column_to_date(df, "doc_date")

    if "reg_date" in df.columns:
        df_column_to_date(df, "reg_date")

    if "resolutions_date2" in df.columns:
        df_column_to_date(df, "resolutions_date2")

    df_column_to_str(df, "supplier_ipn")
    df_column_to_str(df, "supplier_okpo")
    df_column_to_str(df, "customer_ipn")
    df_column_to_str(df, "customer_okpo")

    return df


# Функция для обработки значений NaT в DataFrame
def handle_nat_values(df):
    return df.dropna(
        subset=[col for col in df.columns if df[col].dtype == "datetime64[ns]"]
    )


def add_filenaname_to_df(df, filename):
    df["file_name"] = filename
    return df


def column_names_save_to_text(df, filename):
    with open("columns.text", "a") as f:
        txt = f"{list(df.columns)};{filename}\n"
        f.write(txt)


def save_df_to_db(dfnew, filename):
    dfnew["file_name"] = filename
    try:
        # Сохранение DataFrame в таблицу PostgreSQL
        dfnew.to_sql(TABLE_NAME, engine, if_exists="append", index=False)
        # print(f"Данные успешно сохранены в таблицу {file}.")
    except Exception as e:
        if "UniqueViolation" in e.__str__():
            # print(f"UniqueViolation: {dfnew.loc[1]['resolutions_number']}")
            pass
        else:
            print(dfnew.values)
            print(f"{file}; dfnew.to_sql: {e}")


def crete_folder_if_not_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


def move_files_to_folder(file_from, path_to_xls_archive):
    crete_folder_if_not_exists(path_to_xls_archive)
    extract_file = os.path.basename(file_from)
    file_exists = os.path.exists(os.path.join(path_to_xls_archive, extract_file))
    if file_exists:
        os.remove(file_from)
    else:
        os.rename(file_from, os.path.join(path_to_xls_archive, extract_file))

if __name__ == '__main__':
    cur_dir = os.path.dirname(__file__)
    folder_path = os.path.join(cur_dir, 'downloads', 'XML')
    files = get_files_by_ext(folder_path, '.xml')
    for file in files:
        # print(file)
        xml_file_path = os.path.join(folder_path, file)
        try:
            df = pd.read_xml(xml_file_path, parser="lxml", encoding="utf-8")
        except UnicodeDecodeError:
            df = pd.read_xml(xml_file_path, parser="lxml", encoding="windows-1251")

        # column_names_save_to_text(df, file)
        type = find_type_by_columns(df)
        if type in ["type1", "type2", "type5", "type6", "type7", "type8", "type9", "type10", "type11", "type12", "type13"]:
            dfnew = df_rename_columns(df, type)
            if not dfnew.empty:
                save_df_to_db(dfnew, file)
        elif not type in ["type3", "type4"]:  # "type4" найти номер решения Н: 7864604/41098985
            print(type)
            pass

        move_files_to_folder(xml_file_path, os.path.join(folder_path, "archive"))
        continue
