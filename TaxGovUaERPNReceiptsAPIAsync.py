# из бд берет данные о блокированных НН/РК
# и по ним скачивает pdf файлы с квитанциями
# этот файл использует асинхронные запросы

import asyncio
import os
import time
from datetime import datetime
from pathlib import Path

import aiofiles
import aiohttp
import pandas as pd
import requests
from dateutil.parser import parse
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

from AsyncHTTP import fetch_url
from AsyncPostgresql import engine, async_save_pg
from ChangeKeyBoard import set_keyboard_layout
from TaxGovUaConfig import get_token
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_RECEIPT_BLOCK, SQL_UPDATE_FILENAME

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

download_dir = os.path.join(cur_dir, "downloads", "pdf", "erpn_code")
os.makedirs(download_dir, exist_ok=True)

driver, token = get_token()
save_pdf_to_path = ''
wrong_urls = []
print(token)

async def async_fetch(session, url):
    async with session.get(url) as response:
        return await response.read()


# извлекает из имени(!) pdf файла уникальный (code, kvt) код и вид квитанции(999,0,1,2,3,4)
def extract_code_and_receiptnumber_from_path(path_pdf):
    arr = Path(path_pdf).stem.split()
    code = arr[0]
    kvt = None
    if code.isdigit():
        code = int(code)
    else:
        code = None

    if len(arr) == 1:
        kvt = 999
        return code, kvt
    elif len(arr) == 2:
        kvt = arr[1]
        if kvt.isdigit():
            kvt = int(kvt)
        else:
            kvt = None
    return code, kvt


def save_all_pdf_filenames_to_df():
    pdf_files_list_without_path = [file for file in os.listdir(download_dir) if file.endswith(".pdf")]
    data = [(file,) + tuple(extract_code_and_receiptnumber_from_path(file)) for file in pdf_files_list_without_path]
    result = asyncio.run(async_save_pg(SQL_UPDATE_FILENAME, data))
    if not result:
        print(f"{result} not saved to db")


async def get_blocks_docs_df():
    sql = """SELECT * FROM t_tax_cabinet_erpn_api_block WHERE COALESCE(filename,'') = '';"""
    df = pd.read_sql(sql, engine)
    return df


def sync_fetch(url, save_pdf_to_path, retries=3):
    global token, driver
    headers = {
        "Authorization": token,
        "Content-Type": "application/pdf",
    }
    for attempt in range(retries):
        try:
            result = requests.get(url, headers=headers, timeout=10)  # Установка таймаута на 10 секунд
            if result.status_code == 200:
                with open(save_pdf_to_path, 'wb') as f:
                    f.write(result.content)
                return save_pdf_to_path
            else:
                print(f"Ошибка при получении данных из url: {result.status_code}\n{url}")
                break
        except Exception as e:
            print(f"Неизвестная ошибка: {url}")

        driver, token = get_token(driver)  # Получаем новый токен
        headers["Authorization"] = token  # Обновляем заголовок с новым токеном
        slp = 5 * (1 + attempt)
        time.sleep(slp)  # Ожидание перед повторной попыткой

    return save_pdf_to_path


async def async_fetch_async(session, url, save_pdf_to_path, timeout, retries=3):
    global token, driver, wrong_urls
    headers = {
        'Authorization': token,
        'Content-Type': 'application/pdf',
    }
    for attempt in range(retries):
        try:
            async with session.get(url, headers=headers, ssl=False, timeout=timeout) as response:
                if response.status == 200:
                    content = await response.read()
                    async with aiofiles.open(save_pdf_to_path, "wb") as f:
                        await f.write(content)
                    if os.path.exists(save_pdf_to_path):
                        print(f"Файл сохранен: {save_pdf_to_path}")
                        return save_pdf_to_path
                else:
                    print(
                        f"Ошибка при получении данных из url: {response.status}, {url}"
                    )
        except aiohttp.ClientError as e:
            print(f"Ошибка при запросе данных: {e}")
        except Exception as e:
            print(f"Неизвестная ошибка: {e}")
        finally:
            headers["Authorization"] = token  # Обновляем заголовок с новым токеном
            slp = 5 * (1 + attempt)
            await asyncio.sleep(slp)  # Асинхронное ожидание перед повторной попыткой

        driver, token = get_token(driver)  # Получаем новый токен
        headers["Authorization"] = token  # Обновляем заголовок с новым токеном
        slp = 5 * (1 + attempt)
        await asyncio.sleep(slp)

    wrong_urls.append(url)

    return save_pdf_to_path


# группировка по 3 запроса, используя семафор.
# Ограничение на 3 одновременные задачи. т.е если одна задача завершится, то запустится следующая,
# но не более 3 одновременно
async def fetch_with_sem(sem, session, url, save_pdf_to_path, timeout):
    async with sem:
        return await async_fetch_async(session, url, save_pdf_to_path, timeout)


async def run_map_get_list(session, urls, timeout=10):
    sem = asyncio.Semaphore(3)  # Ограничение на 3 одновременные задачи
    tasks = [fetch_with_sem(sem, session, url, save_pdf_to_path, timeout) for url, save_pdf_to_path in urls.items()]
    return await asyncio.gather(*tasks)


async def extract_blocks_docs():
    global wrong_urls
    url = f"https://cabinet.tax.gov.ua/ws/api/file/nlnkhd/pdf"
    async with aiohttp.ClientSession() as session:
        df = await get_blocks_docs_df()
        if df.empty:
            # print("В данном периоде нет данных для обработки")
            return

        df["path_doc"] = df.apply(
            lambda row: os.path.join(
                download_dir, f"{row.get('code')} {int(row.get('kvt_number'))}.pdf"
            ),
            axis=1,
        )

        # для НН/РК
        df_doc = df[df["kvt_number"] == 999]
        df_doc["urls_doc"] = df_doc.apply(
            lambda row: url + f"?code={row.get('code')}&impdate={row.get('reg_date')}",
            axis=1,
        )
        urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
        responses = await run_map_get_list(session, urls_doc)

        # для квитанции 1
        df_doc = df[df["kvt_number"] == 1]
        df_doc["urls_doc"] = df_doc.apply(
            lambda row: url
                        + f"/kvt?code={row.get('code')}&impdate={row.get('reg_date')}",
            axis=1,
        )
        urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
        responses = await run_map_get_list(session, urls_doc)

        # для квитанций 2, 3, 4
        df_doc = df[df["kvt_number"].isin([2, 3, 4])]
        df_doc["urls_doc"] = df_doc.apply(
            lambda row: url
                        + f"/kvt{int(row.get('kvt_number'))}?code={row.get('code')}&impdate={row.get('reg_date')}",
            axis=1,
        )
        urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
        responses = await run_map_get_list(session, urls_doc)


def change_value_to_datetime(value):
    if isinstance(value, datetime):
        return value
    elif isinstance(value, str):
        return parse(value)
    elif isinstance(value, pd.Timestamp):
        return value.to_pydatetime()
    elif isinstance(value, int):
        return datetime.fromtimestamp(value)
    else:
        return None


async def save_receipt_data_to_db(row, receipt_date, receipt_number, kvt_number):
    code = row.get("code")
    ijcode = row.get("ijcode")
    reg_date = change_value_to_datetime(row.get("reg_date"))
    receipt_date = change_value_to_datetime(receipt_date)
    reg_number = row.get("reg_number")
    receipt_status = row.get("receipt_status")
    data = (
        code,
        ijcode,
        reg_date,
        reg_number,
        receipt_date,
        receipt_number,
        receipt_status,
        kvt_number
    )
    result = await async_save_pg(SQL_INSERT_RECEIPT_BLOCK, [data])
    if not result:
        print(f"{receipt_number} not saved to db")


if __name__ == '__main__':
    print("Start", datetime.now())
    save_all_pdf_filenames_to_df()
    asyncio.run(extract_blocks_docs())
    print(wrong_urls)
    save_all_pdf_filenames_to_df()
    print("End", datetime.now())
