# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1412602"
SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
      id uuid DEFAULT uuid_generate_v4() NOT NULL,
      hdate date NULL, -- Дата Повідомлення
      hnum int8 NULL, -- Номер Повідомлення
      htin int8 NULL, -- ОКПО/паспорт
      hnpdv numeric(15) NULL, -- ИНН
      hname varchar(100) NULL, -- Назва
      m01 numeric(1) NULL, -- зупинення у зв’язку з виявленням обставин
      m02 numeric(1) NULL, -- з урахуванням інформації та копій відповідних документів
      r02g1d date NULL, -- Дата Док
      r02g2s int8 NULL, -- Номер Док
      m04 numeric(1) NULL, -- невідповідність критеріям ризиковості
      m03 numeric(1) NULL, -- відповідність критеріям ризиковості
      r03g1s varchar NULL, -- Підстава додатку 1
      t3rxxxxg1s varchar NULL, -- Підстава
      m05 numeric(1) NULL, -- Інформація, за якою встановлена відповідність ризиковості
      t5rxxxxg1s varchar NULL, -- Тип операції
      t5rxxxxg21d date NULL, -- Дата з
      t5rxxxxg22d date NULL, -- Дата по
      t5rxxxxg3s varchar(100) NULL, -- УКТЗЕД
      t5rxxxxg4s varchar(100) NULL, -- ДКПП
      t5rxxxxg5s varchar(100) NULL, -- Умовний код товару операції
      t5rxxxxg6s varchar(100) NULL, -- Податков номер платника
      m06 numeric(1) NULL, -- Ненадання платником податку копій документів
      t6rxxxxg1s varchar(200) NULL, -- Документи
      hexecutor varchar(50) NULL, -- голова комісії
      filename varchar(55) NULL,
      CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 4. РІШЕННЯ про відповідність/невідповідність платника критеріям ризиковості';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.htin IS 'ОКПО/паспорт';
    COMMENT ON COLUMN {TABLE_NAME}.hnpdv IS 'ИНН';
    COMMENT ON COLUMN {TABLE_NAME}.hname IS 'Назва';
    COMMENT ON COLUMN {TABLE_NAME}.m01 IS 'зупинення у зв’язку з виявленням обставин';
    COMMENT ON COLUMN {TABLE_NAME}.m02 IS 'з урахуванням інформації та копій відповідних документів';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1d IS 'Дата Док';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2s IS 'Номер Док';
    COMMENT ON COLUMN {TABLE_NAME}.m04 IS 'невідповідність критеріям ризиковості';
    COMMENT ON COLUMN {TABLE_NAME}.m03 IS 'відповідність критеріям ризиковості';
    COMMENT ON COLUMN {TABLE_NAME}.r03g1s IS 'Підстава додатку 1';
    COMMENT ON COLUMN {TABLE_NAME}.t3rxxxxg1s IS 'Підстава';
    COMMENT ON COLUMN {TABLE_NAME}.m05 IS 'Інформація, за якою встановлена відповідність ризиковості';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg1s IS 'Тип операції';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg21d IS 'Дата з';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg22d IS 'Дата по';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg3s IS 'УКТЗЕД';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg4s IS 'ДКПП';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg5s IS 'Умовний код товару операції';
    COMMENT ON COLUMN {TABLE_NAME}.t5rxxxxg6s IS 'Податков номер платника';
    COMMENT ON COLUMN {TABLE_NAME}.m06 IS 'Ненадання платником податку копій документів';
    COMMENT ON COLUMN {TABLE_NAME}.t6rxxxxg1s IS 'Документи';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';
  
"""


async def main_t_tax_cabinet_j1412602_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1412602_async())
