# скачивает решения (НЕ квитанции) в формате pdf с сайта https://cabinet.tax.gov.ua/documents/in

import asyncio
import time

import aiohttp
import pandas as pd

from AsyncPostgresql import engine, async_save_pg
from ScrapeWithLogs import get_bearer_token
from TaxGovUaConfig import refresh_screen, get_token

# Авторизация на сайте
driver, token = get_token()
wrong_urls = []


async def get_token(count=1):
    refresh_screen(driver)
    token = get_bearer_token(driver)
    if not token:
        await asyncio.sleep(10 * count)
        count += 1
        return await get_token(count)
    return token


async def get_erpn_data(session, retries=5):
    global token
    url_const = 'https://cabinet.tax.gov.ua/ws/api/post/incoming?type=reestr&page='
    df = pd.DataFrame()
    headers = {'Authorization': token, "Content-Type": "application/json"}
    current_page = 0
    total_pages = 1
    while current_page < total_pages:
        attempt = 1
        url = url_const + f"{current_page}&sort=dateIn,desc&size=2000"
        while attempt <= retries:
            try:
                async with session.get(url, headers=headers, timeout=10) as result:
                    if result.status == 200:
                        result_json = await result.json()
                        total_pages = result_json.get('totalPages', 1)
                        current_page = result_json.get('number', 0) + 1
                        cur_df = pd.DataFrame(result_json.get('content'))
                        df = pd.concat([df, cur_df], ignore_index=True)
                        break
                    elif result.status == 401:
                        token = await get_token()  # Получаем новый токен
                        headers['Authorization'] = token  # Обновляем заголовок с новым токеном
                    else:
                        print(f"Ошибка при получении данных из url: {await result.text()}")
            except Exception as e:
                print(f"Ошибка при запросе данных: {e}")
                attempt += 1
                await asyncio.sleep(5 * attempt)  # Ожидание перед повторной попыткой
            token = await get_token()  # Получаем новый токен
            headers['Authorization'] = token  # Обновляем заголовок с новым токеном
        else:
            wrong_urls.append(url)
            current_page += 1
    return df


async def main_load_solutions_list_to_db_async():
    async with aiohttp.ClientSession() as session:
        df = await get_erpn_data(session)
        df.columns = [col.lower() for col in df.columns]
        df.to_sql("t_tax_cabinet_solutions", engine, if_exists="append", index=False)
        await async_save_pg("SELECT fn_remove_duplicates('t_tax_cabinet_solutions');")


if __name__ == "__main__":
    try:
        print(f"Start at {time.ctime()}")
        asyncio.run(main_load_solutions_list_to_db_async())
    except Exception as e:
        print(f"Ошибка при выполнении программы: {e}")
    finally:
        driver.quit()
        print(f"Wrong urls: {wrong_urls}")
        print(f"End at {time.ctime()}")
