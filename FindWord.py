# нечеткое сравнение слов
# pip install fuzzywuzzy python-Levenshtein

from fuzzywuzzy import process


def find_word_position(text, search_terms):
    # Ищем лучшее совпадение с текстом
    best_match, score = process.extractOne(text, search_terms)

    if best_match:
        # Найденное слово
        print(f'Найдено: "{best_match}" с совпадением {score}%')

        # Определение позиции найденного совпадения
        # Используем стандартный поиск подстроки, чтобы найти её позицию
        start_pos = text.lower().find(best_match.lower())

        if start_pos != -1:
            return start_pos
            # print(f'Позиция начала: {start_pos}')
            # print(f'Позиция конца: {start_pos + len(best_match) - 1}')
        # else:
        #     print('Подстрока не найдена точно.')
    else:
        return None
        # print('Совпадений не найдено')


if __name__ == '__main__':
    # Строка, в которой мы ищем
    text = 'Вивіз сміття (ТПВ) з 23.07.2024 по 24.08.2024 р.  1 2 Оренда контейнера 1,1 м.куб  Кіл-сть  5,00 1,00  Од.  конт шт  Всього найменувань 2, на суму 2 000,00 грн. Дві тисячі гривень 00 копійок У т.ч ПДВ Триста тридцять три гривні 33 копійки  Код одиниці Ціна з ПДВ Знижка  2049 2009  300,00 500,00 Знижка: Всього: У тому числі ПДВ:  - -  Сума з ПДВ 1 500,00 500,00 - 2 000,00 333,33  Виписав(ла):  -  Рахунок дійсний до -  \x0c'

    # Список возможных вариантов, которые нужно искать
    search_terms = 'куб'

    # Вызов функции для поиска слова
    position = find_word_position(text, search_terms)
    print(f'Позиция начала: {position}')
