import smtplib
from email import encoders
from email.header import Header
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formatdate, formataddr
from urllib.parse import quote


def send_email(sender_email, sender_name, receiver_email, subject, body, file_path, file_name):
    # Создаем сообщение
    msg = MIMEMultipart()
    msg['From'] = formataddr((str(Header(sender_name.strip(), 'utf-8')), sender_email.strip()))
    msg['To'] = receiver_email.strip()
    msg['Subject'] = Header(subject.strip(), 'utf-8')
    msg['Date'] = formatdate(localtime=True)

    # Добавляем текст письма
    msg.attach(MIMEText(body, 'plain', 'utf-8'))

    # Открываем PDF файл для прикрепления
    with open(file_path, "rb") as attachment:
        part = MIMEBase("application", "pdf")
        part.set_payload(attachment.read())

    # Кодируем файл в base64
    encoders.encode_base64(part)
    encoded_file_name = quote(file_name.encode('utf-8'))
    part.add_header('Content-Disposition', "attachment; filename*=UTF-8''%s" % encoded_file_name)

    # Прикрепляем файл к письму
    msg.attach(part)

    # Настраиваем SMTP сервер
    try:
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(sender_email, 'bpvk otrs jsln wqlm')  # Убедитесь, что пароль правильный

        # Отправляем письмо
        server.sendmail(sender_email, receiver_email, msg.as_string())
        server.quit()
        print("Email sent successfully!")
        return True
    except Exception as e:
        print(f"Error sending email: {e}")

    return False


if __name__ == "__main__":
    # Пример использования функции
    send_email(
        sender_email="<EMAIL>",
        sender_name="PrestigeBot",
        receiver_email="<EMAIL>",
        subject="Your PDF Document",
        body="Please find the attached PDF document.",
        file_path=r"C:\Rasim\Python\Medoc\Rahunok MM № 608101 vіd 06.08.2024.pdf",
        file_name="Rahunok MM № 608101 vіd 06.08.2024.pdf"
    )
