# данные из ЕРПН через API о НН/РК

import datetime
import os
import time
from pathlib import Path

import pandas as pd
import requests
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

from AsyncPostgresql import engine, con_postgres_psycopg2, save_to_pg
from ChangeKeyBoard import set_keyboard_layout
from ScrapeWithLogs import get_bearer_token
from TaxGovUaConfig import authorizations, refresh_screen, remove_duplicates

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

cur_dir = os.path.dirname(os.path.abspath(__file__))
# download_dir = os.path.join(cur_dir, "downloads", "pdf")
# os.makedirs(download_dir, exist_ok=True)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Авторизация на сайте
authorizations(driver)


def get_date_from_date_format(date):
    return parser.parse(date).date()


def get_token(count=1):
    refresh_screen(driver)
    token = get_bearer_token(driver)
    if not token:
        time.sleep(10 * count)
        count += 1
        get_token(count)

    return token


def get_erpn_data(date_from, date_to, token, page_number=0, retries=5):
    url = (f"https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="
           f"{date_from}%2000:00:00&toImpdate={date_to}%2023:59:59&page={page_number}&sort=impdate")
    headers = {"Authorization": token, "Content-Type": "application/json"}

    for attempt in range(retries):
        try:
            result = requests.get(
                url, headers=headers, timeout=10
            )  # Установка таймаута на 10 секунд
            if result.status_code == 200:
                return (
                    result.json()
                )  # Используем встроенный метод json() вместо json.loads
            elif result.status_code == 401:
                token = get_token()  # Получаем новый токен
                headers["Authorization"] = token  # Обновляем заголовок с новым токеном
            else:
                pass
                # print(f"Ошибка при получении данных из url: {result.text}")
                # break  # Прерываем цикл в случае ошибки, отличной от 401
        except requests.exceptions.RequestException as e:
            pass
            # print(f"Ошибка при запросе данных: {e}\n{url}")
        except Exception as e:
            pass
            # print(f"Неизвестная ошибка: {e}")
            # break  # Прерывание на случай непредвиденной ошибки

        time.sleep(5 * (1 + attempt))  # Ожидание перед повторной попыткой
        token = get_token()  # Получаем новый токен
        headers["Authorization"] = token  # Обновляем заголовок с новым токеном
    
    print(f"Ошибка при получении данных из url:{url}\n{result.text}")
    return None


def parse_and_save_date(date_from, date_to, token, page_count):
    for page_number in range(page_count):
        if page_number == 0:
            page_num, df = get_pages_and_df(date_from, date_to, token)
        else:
            page_num, df = get_pages_and_df(date_from, date_to, token, page_number)
        while True:
            try:
                # *************
                # Преобразование DataFrame в список кортежей для вставки
                records = df.to_dict(orient="records")
                columns = df.columns.tolist()
                values = [tuple(record[col] for col in columns) for record in records]

                # Формирование запроса для вставки данных
                insert_query = f"""
                INSERT INTO t_tax_cabinet_erpn_api ({', '.join(columns)})
                VALUES ({', '.join(['%s' for _ in columns])})
                ON CONFLICT (code, ijcode)
                DO UPDATE SET
                    prdinn = EXCLUDED.prdinn,
                    pkpinn = EXCLUDED.pkpinn,
                    nmr = EXCLUDED.nmr,
                    crtdate = EXCLUDED.crtdate,
                    ndssm = EXCLUDED.ndssm,
                    r4100g11 = EXCLUDED.r4100g11,
                    tin = EXCLUDED.tin,
                    cptin = EXCLUDED.cptin,
                    crcode = EXCLUDED.crcode,
                    corrnmr = EXCLUDED.corrnmr,
                    flags = EXCLUDED.flags,
                    dend = EXCLUDED.dend,
                    ftype = EXCLUDED.ftype,
                    htypr = EXCLUDED.htypr,
                    waitcorr = EXCLUDED.waitcorr,
                    waitrisk = EXCLUDED.waitrisk,
                    impdate = EXCLUDED.impdate,
                    hsmcstt = EXCLUDED.hsmcstt,
                    hsmcsttname = EXCLUDED.hsmcsttname,
                    docrnn = EXCLUDED.docrnn,
                    hnamesel = EXCLUDED.hnamesel,
                    hnamebuy = EXCLUDED.hnamebuy,
                    kvt2 = EXCLUDED.kvt2,
                    kvt3 = EXCLUDED.kvt3,
                    kvt4 = EXCLUDED.kvt4
                """

                # *************
                result = save_to_pg(insert_query, values)
                # df.to_sql("t_tax_cabinet_erpn_api", engine, if_exists='append', index=False, method='multi')
                if result:
                    print(
                        f"Сохранено {date_from} {date_to}, стр {page_number + 1}/{page_count}"
                    )
                break
            except Exception as e:
                print(f"Ошибка при сохранении данных в базу данных: {e}")
                time.sleep(5)


def get_pages_and_df(date_from, date_to, token, page_number=0):
    pages_count = 0
    df = pd.DataFrame()
    while True:
        if not token:
            token = get_token()
        result = get_erpn_data(date_from, date_to, token, page_number)
        if isinstance(result, dict) and not result.get("content"):
            df = pd.DataFrame(result.get("content"))
        elif isinstance(result, dict) and result.get("content"):
            pages_count = int(result.get("totalPages"))
            df = pd.DataFrame(result.get("content"))

        if not df.empty:
            df.columns = [col.lower() for col in df.columns]

            # Заменяем NaN значения на 0
            df.fillna(0, inplace=True)

            df["crtdate"] = pd.to_datetime(df["crtdate"])
            df["impdate"] = pd.to_datetime(df["impdate"])
            df["dend"] = pd.to_datetime(df["dend"])
            df["code"] = abs(df["code"].astype(int))
            df["prdinn"] = abs(df["prdinn"].astype(int))
            df["pkpinn"] = abs(df["pkpinn"].astype(int))
            df["nmr"] = abs(df["nmr"].astype(int))
            df["tin"] = abs(df["tin"].astype(int))
            df["cptin"] = abs(df["cptin"].astype(int))
            df["ijcode"] = abs(df["ijcode"].astype(int))
            df["crcode"] = abs(df["crcode"].astype(int))
            df["corrnmr"] = abs(df["corrnmr"].astype(int))
            df["flags"] = abs(df["flags"].astype(int))
            df["ftype"] = abs(df["ftype"].astype(int))
            df["htypr"] = abs(df["htypr"].astype(int))
            df["waitcorr"] = abs(df["waitcorr"].astype(int))
            df["waitrisk"] = abs(df["waitrisk"].astype(int))
            df["hsmcstt"] = abs(df["hsmcstt"].astype(int))
            df["kvt2"] = abs(df["kvt2"].astype(int))
            df["kvt3"] = abs(df["kvt3"].astype(int))
            df["kvt4"] = abs(df["kvt4"].astype(int))

        if page_number is None or page_number == 0:
            return pages_count, df
        else:
            return page_number, df


def all_erpn_docs_api(date_from, date_to):
    date_from = get_date_from_date_format(date_from)
    date_to = get_date_from_date_format(date_to)
    token = get_token()
    print(token)
    while date_from <= date_to:
        # print(f"Обработка даты {date_from}")
        date_current = date_from.strftime("%Y-%m-%d")
        page_count, df = get_pages_and_df(date_current, date_current, token)
        if page_count:
            parse_and_save_date(date_from, date_to, token, page_count)
        date_from += relativedelta(days=1)
    remove_duplicates("t_tax_cabinet_erpn_api")


if __name__ == "__main__":
    print(f"Start at {time.ctime()}")
    date_to = datetime.datetime.now().strftime("%Y-%m-%d")
    date_from = (datetime.datetime.now() - relativedelta(months=12)).strftime("%Y-%m-%d")
    all_erpn_docs_api(date_from, date_to)
    driver.quit()
    print(f"End at {time.ctime()}")
