import asyncio
import base64
import os

import pandas as pd
from transliterate import translit

from AsyncHTTP import fetch_one_url
from AsyncPostgresql import engine, async_save_pg, hostname_public
from ReadPDFByPDFLumber import extract_column_data_with_laparams
from ReadPDFGetAccountNumber import find_account_numbers
from ReadPDFMiner import extract_column_with_pdfminer
from SendMail import send_email

TABLE_NAME = 't_medoc_sent_document'

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_id varchar(40) NOT NULL,
        customer_edrpou varchar(15) DEFAULT NULL,
        customer_name varchar(100) DEFAULT NULL,
        customer_account varchar(30) DEFAULT NULL,
        file_name varchar(100) NOT NULL,
        doc_number varchar(20) DEFAULT NULL,
        doc_date date DEFAULT NULL,
        amount numeric(15,2) DEFAULT 0,
        amount_vat numeric(15,2) DEFAULT 0,
        manager_email varchar(25) DEFAULT NULL,
        payment_purpose varchar(300) DEFAULT NULL,
        date_send timestamp with time zone DEFAULT now(),        
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (doc_id)
    );

    COMMENT ON TABLE {TABLE_NAME} IS 'docid отправленных на почту менеджерам документов';
    COMMENT ON COLUMN {TABLE_NAME}.doc_id IS 'id документа';
    COMMENT ON COLUMN {TABLE_NAME}.customer_edrpou IS 'код контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.customer_name IS 'наименование контрагента';    
    COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'номер документа';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата документа';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма документа';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'сумма НДС';
    COMMENT ON COLUMN {TABLE_NAME}.manager_email IS 'email менеджера';
    COMMENT ON COLUMN {TABLE_NAME}.date_send IS 'дата отправки email';
    COMMENT ON COLUMN {TABLE_NAME}.payment_purpose IS 'назначение платежа';
    COMMENT ON COLUMN {TABLE_NAME}.customer_account IS 'расчетный счет контрагента';

"""

SQL_INSERT_DOCID = f"""
    INSERT INTO {TABLE_NAME} 
    (
        doc_id, 
        customer_edrpou,
        customer_name, 
        customer_account,
        file_name, 
        doc_number, 
        doc_date, 
        amount, 
        amount_vat,
        manager_email,
        payment_purpose
    ) 
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    ON CONFLICT (doc_id) DO NOTHING
    ;
"""


async def create_table():
    await async_save_pg(SQL_CREATE_TABLE)


def cyrillic_to_latin(text):
    return translit(text, 'ru', reversed=True)


# из Медка получаем счета с датой больше 01.12.2024 которые еще не отправлены менеджерам
async def get_unsent_data():
    df = pd.read_sql(
        f"""
            SELECT DISTINCT
                * 
            FROM v_medoc_reestr_and_docinfo 
            WHERE doc_date::date >= '01.12.2024'::date
                AND doc_type_name IN ('Счет','Взаимозачет')
                AND doc_id NOT IN (SELECT doc_id FROM {TABLE_NAME})
            ;
            """,
        engine)
    return df


# извлекаем почту менеджера и наименование контрагента
def get_manager_info(client_code):
    df = pd.read_sql(
        f"""
            SELECT DISTINCT
                customer,
                a_comment as email 
            FROM v_one_manager_counterparty_contracts_segments 
            WHERE edrpou = '{client_code}'
            ;
            """,
        engine)

    if df.empty:
        return None
    return df['customer'].values[0], df['email'].values[0]


def get_customer_accounts():
    df = pd.read_sql(
        f"""
        SELECT 
            account_number as account
        FROM t_one_cat_cash_bank_accounts AS accounts
            INNER JOIN
            (
                SELECT
                    primary_bank_account_key 
                FROM public.t_one_cat_organizations x
                WHERE primary_bank_account_key <> '********-0000-0000-0000-************'
            ) AS customer
                ON customer.primary_bank_account_key = accounts.ref_key
        """,
        engine)

    if df.empty:
        return None
    return ' '.join(df['account'].values)


def delete_file(file_name):
    try:
        os.remove(file_name)
    except FileNotFoundError:
        pass


def send_mail_with_attachment(customer_edrpou, customer_name, to_list, file_name):
    file_path = os.path.abspath(file_name)
    sender_email = "<EMAIL>"
    sender_name = "PrestigeBot"
    subject = f"{customer_edrpou} {file_name}"
    body = f'ОКПО {customer_edrpou}\n{customer_name}\n{file_name} во вложении.'
    return send_email(sender_email, sender_name, to_list, subject, body, file_path, file_name)


async def main_medoc_save_doc_to_pdf():
    await create_table()
    df = await get_unsent_data()
    print(f'Количество документов для отправки: {len(df)}')
    doc_index = 1
    for index, row in df.iterrows():
        doc_id = row.get('doc_id')
        url = f'http://{hostname_public}:63777/api/Info/PrintDocPDF?idOrg=781&docID={doc_id}&facsimile=true'
        response = await fetch_one_url(url)
        file_name = response[0].get('FileName')
        # file_name = cyrillic_to_latin(file_name)
        file_raw = response[0].get('File')
        customer_name_in = row.get('partner_name')  # у некоторых контрагентов показывает None
        customer_edrpou = row.get('partner_edrpou')
        doc_date = row.get('doc_date')
        doc_number = row.get('doc_num')
        amount = row.get('docsum')
        amount_vat = row.get('vatsum')
        customer_name, manager_email = get_manager_info(customer_edrpou)
        if customer_name_in:
            customer_name = customer_name_in
        # Декодирование файла из base64
        file_data = base64.b64decode(file_raw)

        # Запись файла
        with open(file_name, 'wb') as f:
            f.write(file_data)

        exclude_accounts = get_customer_accounts()
        account_number = find_account_numbers(file_name, exclude_accounts)
        begin_word = ''
        finish_word = ''
        column_name = '№'
        payment_purpose_raw = extract_column_data_with_laparams(file_name, column_name)
        if payment_purpose_raw:
            begin_word = payment_purpose_raw[:5]
            finish_word = payment_purpose_raw[-5:]
        elif customer_edrpou == '********':
            column_name = 'Абонплата'

        payment_purpose = extract_column_with_pdfminer(file_name, column_name, begin_word, finish_word)
        if payment_purpose:
            purpose_payment = payment_purpose
        else:
            purpose_payment = payment_purpose_raw
        if purpose_payment:
            purpose_payment = purpose_payment.replace('\n', '; ')

        if manager_email:
            to_list = [manager_email, '<EMAIL>']
            for email in to_list:
                is_sent = send_mail_with_attachment(customer_edrpou, customer_name, email, file_name)
                if not is_sent:
                    print(f'Ошибка отправки файла {file_name} на почту {email}')
                    continue

        data = (
            doc_id,
            customer_edrpou,
            customer_name,
            account_number,
            file_name,
            doc_number,
            doc_date,
            amount,
            amount_vat,
            manager_email,
            purpose_payment
        )
        # Сохранение doc_id в таблицу
        is_saved = await async_save_pg(SQL_INSERT_DOCID, [data])
        if not is_saved:
            print(f'Документ {file_name} уже есть в таблице {TABLE_NAME}')

        print(f'{doc_index}: Файл {file_name} сохранен')
        doc_index += 1
        pass

        # delete_file(file_name)


if __name__ == '__main__':
    asyncio.run(main_medoc_save_doc_to_pdf())
