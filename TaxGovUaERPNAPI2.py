# pip install grequests
import gevent.monkey

# patсh_all установить в самом начале скрипта
# Устанавливаем патчинг, исключая патчинг потоков#
gevent.monkey.patch_all(thread=False, select=False)
import os

os.environ["GEVENT_SUPPORT"] = "True"
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API
import pandas as pd
import asyncio
import numpy as np
from AsyncPostgresql import async_save_pg
from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
from dateutil.relativedelta import relativedelta
from dateutil import parser
import grequests  # вначале установите библиотеку grequests, потом requests
from datetime import datetime
from TaxGovUaConfig import get_token, remove_duplicates

url_const = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="

driver, token = get_token()
wrong_urls = []


# Получение количества страниц в данном периоде
def get_page_count(date_from, date_to, error_count=3):
    global url_const, token
    url = f"{url_const}{date_from}%2000:00:00&toImpdate={date_to}%2023:59:59&page=0&sort=impdate"
    headers = {'Authorization': token, "Content-Type": "application/json"}
    try:
        while True and error_count > 0:
            result = grequests.get(url, headers=headers, timeout=10)
            if result is None:
                wrong_urls.append(url)
                return []
            for response in grequests.map([result]):
                try:
                    if response and response.status_code == 200:
                        return [response.json()["totalPages"], response.json()["totalElements"]]
                except Exception as e:
                    print(f"Ошибка response: {e}\n{url}")
            _, token = get_token(driver=driver)
            headers['Authorization'] = token
            error_count -= 1
    except Exception as e:
        print(f"Ошибка при получении данных: {e}\n{url}")

    wrong_urls.append(url)
    return []


def exception_handler(request, exception):
    print(f"Request failed: {exception}")
    wrong_urls.append(request.url)


def get_urls(date_from, date_to, page_count):
    urls = []
    for page_number in range(page_count):
        url = f"{url_const}{date_from}%2000:00:00&toImpdate={date_to}%2023:59:59&page={page_number}&sort=impdate"
        urls.append(url)
    return urls


def get_values(response):
    results = [r.json().get("content") for r in response if r]
    df = pd.DataFrame([r for res in results for r in res])
    if not df.empty:
        df.columns = [col.lower() for col in df.columns]

        # Заменяем NaN значения на 0
        df.fillna(0, inplace=True)

        df["crtdate"] = pd.to_datetime(df["crtdate"]).dt.date
        df["impdate"] = pd.to_datetime(df["impdate"])
        df["dend"] = pd.to_datetime(df["dend"]).dt.date
        df["nmr"] = df["nmr"].astype(np.int64)
        df["corrnmr"] = df["corrnmr"].astype(np.int64)

        # Преобразование DataFrame в список кортежей для вставки
        records = df.to_dict(orient="records")
        columns = df.columns.tolist()
        values = [tuple(record[col] for col in columns) for record in records]
        return values
    return []


def process_dates(date_from, date_to):
    global token
    date_from = parser.parse(date_from).date()
    date_to = parser.parse(date_to).date()
    while date_from <= date_to:
        # print(token)
        current_date = date_from.strftime("%Y-%m-%d")
        print(f"Обработка даты {current_date}")
        result = get_page_count(current_date, current_date)

        # Если результат пустой, или док-в в данном дне нет, то переходим к следующему дню
        if not result or result[1] == 0:
            date_from = date_from + relativedelta(days=1)
            continue

        page_count = result[0]

        # формируем отдельный документ для каждой страницы к данной дате
        urls = get_urls(current_date, current_date, page_count)
        _, token = get_token(driver=driver)
        response = grequests.map(
            [
                grequests.get(
                    url,
                    headers={
                        "Authorization": token,
                        "Content-Type": "application/json",
                    },
                    timeout=10,
                )
                for url in urls
            ]
        )

        if response and response[0] is not None and response[0].status_code != 200:
            url = response[0].url
            print(f"Ошибка при получении данных: {response[0].status_code}\n{url}")
            wrong_urls.append(url)
            date_from = date_from + relativedelta(days=1)
            continue

        data = get_values(response)
        result = asyncio.run(async_save_pg(SQL_INSERT_ERPN, data))
        # print(f"Data saved: {result}")
        date_from = date_from + relativedelta(days=1)

    result = remove_duplicates('t_tax_cabinet_erpn_api')
    print(f"Remove duplicates: {result}")


def main(date_from, date_to):
    process_dates(date_from, date_to)
    if not wrong_urls:
        return
    urls = wrong_urls

    print(f"Обработка некорректных URL: {urls}")
    _, token = get_token(driver=driver)
    response = grequests.map(
        [grequests.get(url, headers={'Authorization': token, "Content-Type": "application/json"}, timeout=10) for url in
         urls])
    data = get_values(response)
    if not data:
        print("Нет данных для обработки для сохранения по некорректным URL")
        return
    result = asyncio.run(async_save_pg(SQL_INSERT_ERPN, data))
    print(f"{result}, Занесены некорректные URL")

    result = asyncio.run(async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API))
    print(f"{result}, ДАнные добавлены в таблицу t_tax_cabinet_erpn_api_block")


if __name__ == '__main__':
    print("Start", datetime.now())
    date_from = "2019-01-01"
    date_to = datetime.now().strftime("%Y-%m-%d")
    urls = main(date_from, date_to)
    driver.quit()
    print("End", datetime.now())
