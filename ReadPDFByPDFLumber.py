import pdfplumber


def extract_column_data_with_laparams(pdf_path, column_name):
    laparams = {
        'char_margin': 1.0  # Настройка для разделения символов
    }
    with pdfplumber.open(pdf_path, laparams=laparams) as pdf:
        for page_num, page in enumerate(pdf.pages):
            tables = page.extract_tables()
            if tables:
                result = []
                for table in tables:
                    headers = table[0]
                    col_index = 0
                    if column_name in headers:
                        # print(f'Данные из колонки "{column_name}":')
                        for row in table[1:]:
                            if row[0]:
                                item_value = ''
                                for item in row:
                                    if item and len(item) > len(item_value):
                                        item_value = item
                                if item_value:
                                    clean_data = " ".join(item_value.split())
                                    result.append(clean_data)
                        return ' '.join(result)
    print(f'Колонка "{column_name}" не найдена.')


if __name__ == '__main__':
    # # Путь к PDF файлу
    pdf_path = r"D:\Prestige\Python\Medoc\downloads\24134 2024.04.19.pdf"
    column_name = "Дата складання"
    #
    # # Вызов функции для извлечения данных
    extract_column_data_with_laparams(pdf_path, column_name)
