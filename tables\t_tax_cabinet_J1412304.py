# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1412404"
SQL_CREATE_TABLE = f"""
-- DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    id uuid DEFAULT uuid_generate_v4 (),
    HDATE date NULL,  -- Дата Повідомлення
    HNUM varchar(30) NULL,  -- Номер Повідомлення
    R01G1D date NULL,  -- Дата складання
    R01G21 int8 NULL,  -- Номер ПН
    R01G22 varchar(10) NULL,  -- Номер РК
    R01G3S varchar(35) NULL,  -- Тип Документа
    R01G4 numeric(15,2) NULL,  -- Сума с ПДВ
    R01G5 numeric(15,2) NULL,  -- ПДВ
    R02G1S varchar(15) NULL,  -- ОКПО продавця
    R02G2 numeric NULL,  -- ИНН продавця
    R02G3S varchar(100) NULL,  -- Назва продавця
    R02G4D date NULL,  -- Дата реєстрації ПДВ
    R02G5D date NULL,  -- Дата припинення реєстрації ПДВ
    R03G1S varchar(15) NULL,  -- ОКПО покупця
    R03G2 numeric NULL,  -- ИНН покупця
    R03G3S varchar(100) NULL,  -- Назва покупця
    R03G4D date NULL,  -- Дата реєстрації ПДВ
    R03G5D date NULL,  -- Дата припинення реєстрації ПДВ
    M0100 numeric(1) NULL,  -- реєстровано
    M0200 numeric(1) NULL,  -- відмовлено
    M0210 numeric(1) NULL,  -- ненадання підтвердження інформації
    R0210G1S varchar(15) NULL,  -- додаткова информація
    M0220 numeric(1), -- копії документів
    M0221 numeric(1), -- договорів зокрема зовнішньоекономічних контрактів, з додатками до них
    M0222 numeric(1), -- договорів,  довіреностей,  актів  керівного  органу  платника  податку
    M0223 numeric(1), -- первинних документів щодо постачання/придбання товарів / послуг
    M0224 numeric(1), -- розрахункових документів
    M0225 numeric(1), -- документів відповідності, сертифікації, якості
    R0220G1S varchar, -- Додаткова інформація
    M0230 numeric(1), --  копій документів, складених із порушенням законодавства
    R0230G1S varchar, -- Додаткова інформація
    HEXECUTOR varchar(50), -- голова комісії
    CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
  );
  
  COMMENT ON TABLE {TABLE_NAME} IS 'про реєстрацію / відмову в реєстрації ПН, РК';
  COMMENT ON COLUMN {TABLE_NAME}.HDATE IS 'Дата Повідомлення';
  COMMENT ON COLUMN {TABLE_NAME}.HNUM IS 'Номер Повідомлення';
  COMMENT ON COLUMN {TABLE_NAME}.R01G1D IS 'Дата складання';
  COMMENT ON COLUMN {TABLE_NAME}.R01G21 IS 'Номер ПН';
  COMMENT ON COLUMN {TABLE_NAME}.R01G22 IS 'Номер РК';
  COMMENT ON COLUMN {TABLE_NAME}.R01G3S IS 'Тип Документа';
  COMMENT ON COLUMN {TABLE_NAME}.R01G4 IS 'Сума с ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.R01G5 IS 'ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.R02G1S IS 'ОКПО продавця';
  COMMENT ON COLUMN {TABLE_NAME}.R02G2 IS 'ИНН продавця';
  COMMENT ON COLUMN {TABLE_NAME}.R02G3S IS 'Назва продавця';
  COMMENT ON COLUMN {TABLE_NAME}.R02G4D IS 'Дата реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.R02G5D IS 'Дата припинення реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.R03G1S IS 'ОКПО покупця';
  COMMENT ON COLUMN {TABLE_NAME}.R03G2 IS 'ИНН покупця';
  COMMENT ON COLUMN {TABLE_NAME}.R03G3S IS 'Назва покупця';
  COMMENT ON COLUMN {TABLE_NAME}.R03G4D IS 'Дата реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.R03G5D IS 'Дата припинення реєстрації ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.M0100 IS 'реєстровано';
  COMMENT ON COLUMN {TABLE_NAME}.M0200 IS 'відмовлено';
  COMMENT ON COLUMN {TABLE_NAME}.M0210 IS 'ненадання підтвердження інформації';
  COMMENT ON COLUMN {TABLE_NAME}.R0210G1S IS 'додаткова информація';
  COMMENT ON COLUMN {TABLE_NAME}.M0220 IS 'копії документів';
  COMMENT ON COLUMN {TABLE_NAME}.M0221 IS 'договорів зокрема зовнішньоекономічних контрактів, з додатками до них';
  COMMENT ON COLUMN {TABLE_NAME}.M0222 IS 'договорів,  довіреностей,  актів  керівного  органу  платника  податку';
  COMMENT ON COLUMN {TABLE_NAME}.M0223 IS 'первинних документів щодо постачання/придбання товарів / послуг';
  COMMENT ON COLUMN {TABLE_NAME}.M0224 IS 'розрахункових документів';
  COMMENT ON COLUMN {TABLE_NAME}.M0225 IS 'документів відповідності, сертифікації, якості';
  COMMENT ON COLUMN {TABLE_NAME}.R0220G1S IS 'Додаткова інформація';
  COMMENT ON COLUMN {TABLE_NAME}.M0230 IS 'копій документів, складених із порушенням законодавства';
  COMMENT ON COLUMN {TABLE_NAME}.R0230G1S IS 'Додаткова інформація';
  COMMENT ON COLUMN {TABLE_NAME}.HEXECUTOR IS 'голова комісії';  
"""


async def main_t_tax_cabinet_j1412404_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1412404_async())
