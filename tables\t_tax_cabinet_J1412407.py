# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1412407"
SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
      id uuid DEFAULT uuid_generate_v4() NOT NULL,
      hdate date NULL, -- Дата Повідомлення
      hnum varchar(30) NULL, -- Но<PERSON><PERSON>р Повідомлення
      r01g1d date NULL, -- Да<PERSON><PERSON> складання
      r01g21 int8 NULL, -- Номер ПН
      r01g22 varchar(10) NULL, -- Номер РК
      r01g3s varchar(35) NULL, -- Тип Документа
      r01g4 numeric(15, 2) NULL, -- Сума с ПДВ
      r01g5 numeric(15, 2) NULL, -- ПДВ
      r02g1s int8 NULL, -- ОКПО продавця
      r02g2 int8 NULL, -- ИНН продавця
      r02g3s varchar(100) NULL, -- Назва продавця
      r02g4d date NULL, -- Дата реєстрації ПДВ
      r02g5d date NULL, -- Дата припинення реєстрації ПДВ
      r03g1s int8 NULL, -- ОКПО покупця
      r03g2 int8 NULL, -- ИНН покупця
      r03g3s varchar(100) NULL, -- Назва покупця
      r03g4d date NULL, -- Дата реєстрації ПДВ
      r03g5d date NULL, -- Дата припинення реєстрації ПДВ
      m0100 numeric(1) NULL, -- реєстровано
      m0200 numeric(1) NULL, -- відмовлено
      m0209 numeric(1) NULL, -- ненадання письмових пояснень та копій документів
      m0230 numeric(1) NULL, -- надання док-в із порушенням законодавства
      r0230g1s varchar NULL, -- Додаткова інформація
      hsti varchar(50) NULL, -- органу ДПС
      hexecutor varchar(50) NULL, -- голова комісії
      filename varchar(55) NULL,
      CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 1. про реєстрацію/відмову в реєстрації ПН, РК';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.r01g1d IS 'Дата складання';
    COMMENT ON COLUMN {TABLE_NAME}.r01g21 IS 'Номер ПН';
    COMMENT ON COLUMN {TABLE_NAME}.r01g22 IS 'Номер РК';
    COMMENT ON COLUMN {TABLE_NAME}.r01g3s IS 'Тип Документа';
    COMMENT ON COLUMN {TABLE_NAME}.r01g4 IS 'Сума с ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r01g5 IS 'ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1s IS 'ОКПО продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2 IS 'ИНН продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g3s IS 'Назва продавця';
    COMMENT ON COLUMN {TABLE_NAME}.r02g4d IS 'Дата реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r02g5d IS 'Дата припинення реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r03g1s IS 'ОКПО покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g2 IS 'ИНН покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g3s IS 'Назва покупця';
    COMMENT ON COLUMN {TABLE_NAME}.r03g4d IS 'Дата реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.r03g5d IS 'Дата припинення реєстрації ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.m0100 IS 'реєстровано';
    COMMENT ON COLUMN {TABLE_NAME}.m0200 IS 'відмовлено';
    COMMENT ON COLUMN {TABLE_NAME}.m0209 IS 'ненадання письмових пояснень та копій документів';
    COMMENT ON COLUMN {TABLE_NAME}.m0230 IS 'надання док-в із порушенням законодавства';
    COMMENT ON COLUMN {TABLE_NAME}.r0230g1s IS 'Додаткова інформація';
    COMMENT ON COLUMN {TABLE_NAME}.hsti IS 'органу ДПС';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';
"""


async def main_t_tax_cabinet_j1412407_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1412407_async())
