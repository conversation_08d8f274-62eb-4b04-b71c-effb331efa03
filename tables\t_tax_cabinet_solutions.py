# https://cabinet.tax.gov.ua/tax-invoices/written
# информация взятая из квитанций в формате pdf о дате и статусе блокировки

import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_solutions_xml"
SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        c_doc varchar(10) NULL,
        c_doc_cnt int4 NULL,
        c_doc_stan int4 NULL,
        c_doc_sub int4 NULL,
        c_doc_type int4 NULL,
        c_doc_ver int4 NULL, -- типДодатка
        c_raj float8 NULL,
        c_reg float8 NULL,
        c_sti_orig int4 NULL, -- ГНИ
        d_fill date NULL, -- датаРешения
        filename varchar(100) NULL, -- с какого файла взята инф
        hdate date NULL, -- датаРешения
        hdate1 date NULL, -- датаРешОспоренного
        hdate2 date NULL, -- датаРешенияОспоренного
        hexecutor varchar(50) NULL, -- главаКомиссии
        hname varchar(200) NULL, -- поставщик
        hnpdv int8 NULL, -- поставщикИНН
        hnum varchar(50) NULL, -- номРешения
        hnum2 varchar NULL, -- номРешенияОспоренного
        hnumreg float8 NULL, -- номРегОспоренного
        hsti varchar NULL,
        htin int8 NULL, -- поставщикОКПО
        m01 float8 NULL,
        m010 float8 NULL,
        m0100 int4 NULL, -- регистрация налоговой
        m02 float8 NULL,
        m020 float8 NULL,
        m0200 int4 NULL, -- нетБанкВыписок
        m0209 float8 NULL,
        m021 float8 NULL,
        m0210 numeric(1) NULL,  -- тип причины отказа
        m022 float8 NULL,
        m0220 int4 NULL, -- нетПервичнДок-в
        m0221 float8 NULL,
        m0223 int4 NULL, -- НетДокПодтверждающий
        m02231 numeric(1) NULL,
        m022311 numeric(1) NULL,
        m022312 float8 NULL,
        m022313 numeric(1) NULL,
        m022314 float8 NULL,
        m022315 float8 NULL,
        m022316 numeric(1) NULL,
        m02232 float8 NULL,
        m02234 float8 NULL,
        m02235 numeric(1) NULL,
        m0224 float8 NULL,
        m0225 float8 NULL,
        m0226 numeric(1) NULL,
        m023 float8 NULL,
        m0230 float8 NULL,
        m03 float8 NULL,
        m04 float8 NULL,
        m06 float8 NULL,
        period_month int4 NULL,
        period_type float8 NULL,
        period_year int4 NULL,
        r01g1d date NULL, -- датаРег
        r01g1s varchar NULL,
        r01g21 int8 NULL, -- номерНН/РК
        r01g2d date NULL, -- датаРегТаб
        r01g2s int8 NULL, -- номРег
        r01g3s varchar(100) NULL, -- типДок/номРешенТаб
        r01g4 float8 NULL, -- сумма
        r01g4s varchar(100) NULL, -- типДок
        r01g5 float4 NULL, -- НДС
        r01g6 float4 NULL, -- НДС
        r0210g1s varchar NULL, -- комментарий
        r0220g1s varchar NULL, -- комментарий
        r0230g1s varchar NULL,
        r023g1s varchar NULL,
        r02g1d date NULL, -- датаРешенияТаб
        r02g1s int8 NULL, -- поставщикОКПО
        r02g2 int8 NULL, -- поставщикИНН
        r02g2s varchar(50) NULL, -- намерРешТаб
        r02g3s varchar(100) NULL, -- поставщик
        r02g4d date NULL, -- датаРегПлатНДСПоставщик
        r03g1s varchar NULL, -- клиентОКПО + коммент
        r03g2 int8 NULL, -- клиентИНН
        r03g2s int8 NULL, -- клиентИННродит
        r03g3s varchar(200) NULL, -- контрагент
        r03g4d date NULL, -- датаРегПлательщикомНДСКлиент
        r03g5d date NULL, -- датаРегПлательщикомНДСКлиент
        software text NULL,
        t1rxxxxg1s varchar NULL,
        tin float8 NULL
    );
    
    COMMENT ON COLUMN {TABLE_NAME}.c_doc_ver IS 'типДодатка';
    COMMENT ON COLUMN {TABLE_NAME}.c_sti_orig IS 'ГНИ';
    COMMENT ON COLUMN {TABLE_NAME}.d_fill IS 'датаРешения';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'датаРешения';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'номРешения';
    COMMENT ON COLUMN {TABLE_NAME}.r01g1d IS 'датаРег';
    COMMENT ON COLUMN {TABLE_NAME}.r01g21 IS 'номерНН/РК';
    COMMENT ON COLUMN {TABLE_NAME}.r01g3s IS 'типДок/номРешенТаб';
    COMMENT ON COLUMN {TABLE_NAME}.r01g4 IS 'сумма';
    COMMENT ON COLUMN {TABLE_NAME}.r01g5 IS 'НДС';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1s IS 'поставщикОКПО';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1d IS 'датаРешенияТаб';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2s IS 'намерРешТаб';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2 IS 'поставщикИНН';
    COMMENT ON COLUMN {TABLE_NAME}.r02g3s IS 'поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.r02g4d IS 'датаРегПлатНДСПоставщик';
    COMMENT ON COLUMN {TABLE_NAME}.r03g1s IS 'клиентОКПО + коммент';
    COMMENT ON COLUMN {TABLE_NAME}.r03g2 IS 'клиентИНН';
    COMMENT ON COLUMN {TABLE_NAME}.r01g2d IS 'датаРегТаб';
    COMMENT ON COLUMN {TABLE_NAME}.r03g3s IS 'контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.r03g4d IS 'датаРегПлательщикомНДСКлиент';
    COMMENT ON COLUMN {TABLE_NAME}.m0100 IS 'регистрация налоговой';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'главаКомиссии';
    COMMENT ON COLUMN {TABLE_NAME}.r01g2s IS 'номРег';
    COMMENT ON COLUMN {TABLE_NAME}.m0200 IS 'нетБанкВыписок';
    COMMENT ON COLUMN {TABLE_NAME}.m0220 IS 'нетПервичнДок-в';
    COMMENT ON COLUMN {TABLE_NAME}.m0223 IS 'НетДокПодтверждающий';
    COMMENT ON COLUMN {TABLE_NAME}.r0220g1s IS 'комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.filename IS 'с какого файла взята инф';
    COMMENT ON COLUMN {TABLE_NAME}.hnumreg IS 'номРегОспоренного';
    COMMENT ON COLUMN {TABLE_NAME}.hdate1 IS 'датаРешОспоренного';
    COMMENT ON COLUMN {TABLE_NAME}.hnum2 IS 'номРешенияОспоренного';
    COMMENT ON COLUMN {TABLE_NAME}.hdate2 IS 'датаРешенияОспоренного';
    COMMENT ON COLUMN {TABLE_NAME}.r01g4s IS 'типДок';
    COMMENT ON COLUMN {TABLE_NAME}.r01g6 IS 'НДС';
    COMMENT ON COLUMN {TABLE_NAME}.r03g2s IS 'клиентИННродит';
    COMMENT ON COLUMN {TABLE_NAME}.r03g5d IS 'датаРегПлательщикомНДСКлиент';
    COMMENT ON COLUMN {TABLE_NAME}.htin IS 'поставщикОКПО';
    COMMENT ON COLUMN {TABLE_NAME}.hnpdv IS 'поставщикИНН';
    COMMENT ON COLUMN {TABLE_NAME}.m0210 IS 'тип причины отказа';
    COMMENT ON COLUMN {TABLE_NAME}.hname IS 'поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.r0210g1s IS 'комментарий';

"""


async def main_t_tax_cabinet_erpn_api_block_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_erpn_api_block_async())
