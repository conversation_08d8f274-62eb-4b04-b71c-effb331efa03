import os
import re
import cv2
import pytesseract
from pdf2image import convert_from_path
import numpy as np

# установить путь в PATH
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
poppler_path = r'poppler-24.07.0/Library/bin'


def preprocess_image(image):
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # Apply thresholding
    gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]
    # Remove noise
    gray = cv2.medianBlur(gray, 3)
    return gray


def extract_text_from_pdf(pdf_path):
    # Convert PDF to images
    pages = convert_from_path(pdf_path, 600, poppler_path=poppler_path)
    text = ""
    for page in pages:
        # Convert PIL image to OpenCV format
        open_cv_image = cv2.cvtColor(np.array(page), cv2.COLOR_RGB2BGR)

        # Preprocess the image
        processed_image = preprocess_image(open_cv_image)

        # Extract text with custom configuration for both Ukrainian and English
        custom_config = r'--oem 1 --psm 4'
        text += pytesseract.image_to_string(processed_image, lang='ukr+eng', config=custom_config)
    return text


# извлекаем дату квитанцию. имеет вид - 01.01.2021 01:01:01
# нам нужна только дата - 01.01.2021
# в некоторых квитанциях номер отсутствует в шапке, его надо извлекать из самого текста
# сам номер идет после даты, которую мы извлекли
def get_receipt_date(text):
    pattern = "\d{2}.\d{2}.\d{4} \d{2}:\d{2}:\d{2}"
    date = re.findall(pattern, text)
    if date:
        return date[0]

    return None


def get_number_receipt(text):
    raw_date = get_receipt_date(text)
    pattern = f"{raw_date}.*?(\d+/\d+)|(\d+/\d+/*\d*)"
    raw_number = re.findall(pattern, text)
    if raw_number:
        for nm in raw_number:
            if nm[0]:
                return nm[0]
            if nm[1]:
                return nm[1]
    return None


def extract_receipt_date_and_number(pdf_path):
    extracted_text = extract_text_from_pdf(pdf_path)
    date_receipt = get_receipt_date(extracted_text)
    number_receipt = get_number_receipt(extracted_text)
    print(date_receipt, number_receipt)
    return date_receipt, number_receipt


if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_path = os.path.join(current_dir, r'26570041098985J1201013100000049910220232657.pdf')
    extract_receipt_date_and_number(pdf_path)
