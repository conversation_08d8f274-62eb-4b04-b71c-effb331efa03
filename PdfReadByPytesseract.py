# извлекает из PDF дату и номер решения,
# а также информацию о документе (номер, тип, общая сумма и налоговая сумма)
# переименовывает файл в соответствии с информацией о документе
import os

import cv2
import pytesseract
from pdf2image import convert_from_path
import numpy as np
import re
import asyncio

from AsyncPostgresql import async_save_pg
from tables.t_tax_cabinet_bad_files import SQL_INSERT_BAD_FILE_NAME
from tables.t_tax_cabinet_document_in_from_document import SQL_INSERT_DOCUMENT_IN_FROM_DOCIN

"""
OCR Engine Mode (OEM) Options:
--oem 0: Только старый движок.
--oem 1: Только LSTM (нейронные сети).
--oem 2: Старый + LSTM движки.
--oem 3: По умолчанию, в зависимости от доступности.

Page Segmentation Mode (PSM) Options:
--psm 0: Только определение ориентации и скрипта (OSD).
--psm 1: Автоматическая сегментация страницы с OSD.
--psm 2: Автоматическая сегментация страницы, но без OSD или OCR.
--psm 3: Полностью автоматическая сегментация страницы, но без OSD.
--psm 4: Предполагается один столбец текста переменного размера.
--psm 5: Предполагается один однородный блок вертикально выровненного текста.
--psm 6: Предполагается один однородный блок текста.
--psm 7: Обработка изображения как одной строки текста.
--psm 8: Обработка изображения как одного слова.
--psm 9: Обработка изображения как одного слова в круге.
--psm 10: Обработка изображения как одного символа.
--psm 11: Разреженный текст. Найти как можно больше текста в произвольном порядке.
--psm 12: Разреженный текст с OSD.
--psm 13: Сырой текст. Обработка изображения как одной строки текста, обходя специфические для Tesseract хаки.
"""

# Укажите путь к исполняемому файлу Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Путь к бинарным файлам poppler
# poppler_path = r'C:\PDF\poppler-24.07.0\Library\bin'
poppler_path = r'poppler-24.07.0/Library/bin'


# Функция для извлечения налогового номера поставщика и покупателя
def get_ipn(input_string):
    pattern = r"(\d{12})"
    match = re.findall(pattern, input_string)

    if match:
        try:
            supplier_ipn = match[0]
            customer_ipn = match[1]
            return [supplier_ipn, customer_ipn]
        except Exception as e:
            print(e)

    return None


# Функция для извлечения даты и номера решения
def get_resolution_info(input_string):
    pattern = r"(\d{2}\.\d{2}\.\d{4}).*?(\d+\s\d{8})"
    match = re.search(pattern, input_string)

    if match:
        try:
            date_resolutions = match.group(1)
            number_resolutions = match.group(2).split(' ')[0]
            org_okpo = match.group(2).split(' ')[1]  # match.group(3)
            return [date_resolutions, number_resolutions, org_okpo]
        except Exception as e:
            print(e)

    return None


def get_document_info(input_string):
    # Regular expression to match the date, document number, document type, total amount, and tax amount
    pattern = r"(\d{2}\.\d{2}\.\d{4})\s*\|\s*(\d+)\s*(\w+)\s*\|\s*([\d.]+)\s*([\d.]+)"

    # Search for the pattern in the input string
    match = re.search(pattern, input_string)

    if match:
        try:
            date = match.group(1)
            doc_number = match.group(2)
            doc_type = match.group(3).upper()
            total_amount = match.group(4)
            tax_amount = match.group(5)
            # return f"{doc_type} {doc_number} {date} {total_amount} {tax_amount}"
            return [date, doc_number, doc_type, total_amount, tax_amount]
        except Exception as e:
            print(e)

    return None


def read_pdf(pdf_path):
    # Конвертация PDF в изображения
    images = convert_from_path(pdf_path, poppler_path=poppler_path, dpi=600)
    result = [None, None, None, None, None, None, None, None, None, None]
    # Итерация по каждому изображению (странице)
    for i, image in enumerate(images):
        # Конвертация изображения из формата PIL в формат OpenCV
        image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Предобработка изображения
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]
        gray = cv2.medianBlur(gray, 3)

        # Использование pytesseract для извлечения текста на украинском языке с пользовательской конфигурацией
        custom_config = r'--oem 1 --psm 4'
        text = pytesseract.image_to_string(gray, lang='ukr+eng', config=custom_config)

        # print(f"Страница {i + 1}:\n{text}\n{'-' * 40}")
        resolution_info = get_resolution_info(text)
        if resolution_info:
            # print(resolution_info)
            result = resolution_info
        else:
            result = [None, None, None]

        document_info = get_document_info(text)
        if document_info:
            # print(document_info)
            result = result + document_info
        else:
            result = result + [None, None, None, None, None]

        ipn_info = get_ipn(text)
        if ipn_info:
            result = result + ipn_info
            break
        else:
            result = result + [None, None]

    result.append(pdf_path.split('\\')[-1])
    print(result)
    return result


async def main_tax_cabinet(pdf_path):
    data = read_pdf(pdf_path)
    return await async_save_pg(SQL_INSERT_DOCUMENT_IN_FROM_DOCIN, [data])


async def cycle_read_pdf(pdf_path):
    pdf_files_in_path = [f for f in os.listdir(pdf_path) if f.endswith('.pdf')]
    for pdf_file in pdf_files_in_path:
        result = await main_tax_cabinet(pdf_path + '\\' + pdf_file)
        print(f"{result}, {pdf_file}")


if __name__ == '__main__':
    # Путь к PDF файлам
    current_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_path = os.path.join(current_dir, 'downloads', 'pdf')
    asyncio.run(cycle_read_pdf(pdf_path))
