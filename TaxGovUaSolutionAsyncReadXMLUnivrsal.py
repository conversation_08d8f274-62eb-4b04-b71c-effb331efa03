import asyncio
import os
from pathlib import Path
import re

import pandas as pd
from sqlalchemy import inspect, MetaData, Table, text
from sqlalchemy.exc import SQLAlchemyError

from AsyncPostgresql import async_save_pg, engine
from ReadXML2 import get_files_by_ext

current_path = os.path.dirname(os.path.abspath(__file__))
download_path = os.path.join(current_path, "downloads", "pdf", "solution")
os.makedirs(download_path, exist_ok=True)


async def move_pdf_to_another_folder(old_file):
    archive_dir = os.path.join(download_path, "archive")
    os.makedirs(archive_dir, exist_ok=True)
    new_file = os.path.join(archive_dir, os.path.basename(old_file))

    # Удаление нового файла, если он уже существует
    if os.path.exists(new_file):
        os.remove(new_file)
        
    os.rename(old_file, new_file)
    return new_file


async def read_xml_and_save_to_df(file_path):
    try:
        df = pd.read_xml(file_path, parser="lxml", encoding="utf-8")
    except UnicodeDecodeError:
        df = pd.read_xml(file_path, parser="lxml", encoding="windows-1251")
    except Exception as e:
        print(f"Ошибка при чтении файла {file_path}: {e}")
        df = pd.DataFrame()  # Возвращаем пустой DataFrame в случае ошибки

    df['filename'] = Path(file_path).name
    # Применение функции для заполнения NaN значений в числовых столбцах
    df = df.apply(lambda x: x.fillna(0) if x.dtype.kind in "biufc" else x)
    df.columns = [col.lower() for col in df.columns]

    # Преобразование столбцов в int, затем в str и в datetime
    to_date = [
        "d_fill",
        "hdate",
        "hdate1",
        "hdate2",
        "r01g1d",
        "r01g2d",
        "r02g1d",
        "r02g4d",
        "r03g4d",
        "r03g5d",
    ]
    for col in to_date:
        if col in df.columns:
            df[col] = df[col].astype(int).astype(str)
            
            # Добавление ведущего нуля, если количество символов равно 7
            df[col] = df[col].apply(lambda x: '0' + x if len(x) == 7 else x)
            
            df[col] = pd.to_datetime(df[col], format="%d%m%Y", errors="coerce")

    return df


async def add_missing_columns(engine, table_name, df):
    inspector = inspect(engine)
    existing_columns = [col["name"] for col in inspector.get_columns(table_name)]
    metadata = MetaData()
    table = Table(table_name, metadata, autoload_with=engine)

    with engine.connect() as conn:
        for column in df.columns:
            if column not in existing_columns:
                col_type = df[column].dtype
                if col_type == "int64":
                    col_type = "INTEGER"
                elif col_type == "float64":
                    col_type = "FLOAT"
                elif col_type == "bool":
                    col_type = "BOOLEAN"
                elif col_type == "datetime64[ns]":
                    col_type = "TIMESTAMP"
                else:
                    col_type = "VARCHAR"

                sql = f"ALTER TABLE {table_name} ADD COLUMN {column} {col_type};"
                alter_stmt = text(sql)
                try:
                    # conn.execute(alter_stmt)
                    result = await async_save_pg(sql)
                    print(f"{result}, Добавлена колонка: {column}")
                except SQLAlchemyError as e:
                    print(f"Ошибка при добавлении колонки {column}: {e}")


async def save_df_to_db(df):
    table_name = "t_tax_cabinet_solutions_xml"
    await add_missing_columns(engine, table_name, df)
    try:
        df.to_sql(table_name, engine, if_exists="append", index=False, method="multi")
        # print(f"Данные успешно сохранены в таблицу {table_name}.")
        return True
    except SQLAlchemyError as e:
        print(f"Ошибка при сохранении данных в таблицу {table_name}: {e}")
    
    return False


async def main():
    cur_dir = os.path.dirname(__file__)
    folder_path = os.path.join(cur_dir, "downloads", "pdf", "solution")
    files = get_files_by_ext(folder_path, ".xml")
    for file in files:
        # print(f"Processing file: {file}")
        xml_file_path = os.path.join(folder_path, file)

        # Чтение XML файла
        df = await read_xml_and_save_to_df(xml_file_path)
        if not df.empty:
            result = await save_df_to_db(df)
            if result:
                new_file_path = await move_pdf_to_another_folder(xml_file_path)
                print(f"Файл {file} успешно обработан.")
        else:
            print(f"Файл {file} не был обработан.")


if __name__ == "__main__":
    asyncio.run(main())
