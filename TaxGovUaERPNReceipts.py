# из базы берет список НН/РК со статусами (см список) и загружает квитанции из ЕРПН в формате PDF
# статусы:
# 13
# Реєстрацію зупинено
# Відмовлено за рішенням Комісії


import os
import time
from pathlib import Path

import pyautogui
import pyperclip
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from ChangeKeyBoard import set_keyboard_layout
from ScrapeWithLogs import get_bearer_token
from TaxGovUaConfig import (
    authorizations,
    click_element_by_xpath,
    get_input_value,
    close_window_save_as,
    refresh_screen,
)

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

cur_dir = os.path.dirname(os.path.abspath(__file__))
download_dir = os.path.join(cur_dir, "downloads", "pdf")
os.makedirs(download_dir, exist_ok=True)

prefs = {
    "download.prompt_for_download": True,  # Всегда указывать место для скачивания. False-загрузка без подтверждения
    "download.directory_upgrade": True,  # обновлять существующие директории загрузок.
    "download.default_directory": download_dir,  # Путь для загрузки файлов
    "profile.default_content_setting_values.automatic_downloads": 2,  # Запретить несколько загрузок
    "plugins.always_open_pdf_externally": True,  # PDF-файлы будут открываться внешним просмотрщиком
    #    "profile.default_content_settings.popups": 0,  # Отключить всплывающие окна
}
chrome_options.add_experimental_option("prefs", prefs)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Максимизируем окно браузера
driver.maximize_window()


def open_window_for_save_file(file_path, export_button_xpath, count=1):
    try:
        # Ожидаем появления и кликабельности кнопки
        click_element_by_xpath(driver, export_button_xpath)
        time.sleep(1 * count)

        # Если файл существует, удаляем его
        if os.path.exists(file_path):
            os.remove(file_path)

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        time.sleep(1 * count)
        pyautogui.press("enter")
        print(f"Файл {file_path} загружен")
        return file_path

    except Exception as e:
        return None


# загружает данные в формате Excel с сайта tax.gov.ua по НН и РК
# и сохраняет их в базу данных
def load_receipt(driver, registration_numer, reg_date_first, reg_date_last, index=1, count=0):
    try:

        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/tax-invoices/written")
        refresh_screen(driver)
        registration_numer_path = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[6]/div/input'
        reg_date_first_xpath = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[1]/div/div/div[1]/p-calendar/span/input'
        reg_date_last_xpath = '//*[@id="centerContent"]/app-ti-written/app-ti-search-form/form/div[1]/div[1]/div/div/div[2]/p-calendar/span/input'

        while True:
            date_input = WebDriverWait(driver, 3).until(EC.element_to_be_clickable((By.XPATH, reg_date_first_xpath)))
            date_input.send_keys(Keys.CONTROL + "a")
            date_input.send_keys(Keys.DELETE)

            date_output = WebDriverWait(driver, 3).until(EC.element_to_be_clickable((By.XPATH, reg_date_last_xpath)))
            date_output.send_keys(Keys.CONTROL + "a")
            date_output.send_keys(Keys.DELETE)
            date_output.send_keys(reg_date_last)
            date_input.send_keys(reg_date_first)

            date_input = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, registration_numer_path))
            )
            date_input.send_keys(Keys.CONTROL + "a")
            date_input.send_keys(Keys.DELETE)
            date_input.send_keys(registration_numer)

            value_from_input = get_input_value(driver, reg_date_first_xpath)
            value_from_output = get_input_value(driver, reg_date_last_xpath)
            value_from_input_reg = get_input_value(driver, registration_numer_path)
            if (value_from_input and value_from_output and parser.parse(value_from_input, dayfirst=True)
                    and parser.parse(value_from_output,dayfirst=True) and len(value_from_input_reg) == 10):
                break

        # Находим кнопку "Пошук" и кликаем по ней.
        click_element_by_xpath(driver, "//span[text()='Пошук']")

        # Ожидаем загрузки страницы с таблицей и загружаем квитанцию 1
        reg_date = parser.parse(reg_date_first).strftime("%Y-%m-%d")
        # for index in range(1, 4):
        print(f"Загружаем квитанцию {index}")
        receipt_path = f'//*[@id="centerContent"]/app-ti-written/div[2]/p-table/div/div/table/tbody/tr/td[10]/div/button[{index}]/span[1]'
        bearer_token = get_bearer_token(driver, receipt_path)
        print(bearer_token)

        receipt_save_to = os.path.join(download_dir,
                                       f"receipt_{registration_numer}_{reg_date}_{index}")
        try:
            file_path = open_window_for_save_file(receipt_save_to, receipt_path, 3)
            # Проверяем существование файла. Если не существует, то повторяем попытку 5 раза
            while not os.path.exists(file_path):
                close_window_save_as(driver)
                if count <= 5:
                    count += 1
                    time.sleep(5 * count)
                    file_path = open_window_for_save_file(receipt_save_to, receipt_path, 3)
                else:
                    print(f"Файл {file_path} не загружен")
        except Exception as e:
            print(e)
            pass
        if index < 3:
            index += 1
            load_receipt(driver, registration_numer, reg_date_first, reg_date_last, index)

    except Exception as e:
        pass
        # print(e)

    return False


def main():
    # Авторизация
    authorizations(driver)
    logs = get_bearer_token(driver)
    print(logs)
    registration_numer = "9400990878"
    registration_date = "30.12.2021"
    load_receipt(driver, registration_numer, registration_date, registration_date)

    # Закрываем браузер
    driver.quit()


if __name__ == "__main__":
    main()
