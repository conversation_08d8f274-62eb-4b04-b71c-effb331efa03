import re

import pdfplumber


def find_account_numbers(pdf_path, exclude_accounts=None):
    # Шаблон для поиска номера расчетного счета, который начинается с "UA"
    pattern = r'UA\d{10,27}'

    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            text = page.extract_text()
            text = text.replace(' ', '')
            # Поиск всех номеров расчетных счетов на текущей странице
            account_numbers = re.findall(pattern, text)
            if account_numbers:
                for account in account_numbers:
                    if exclude_accounts and account not in exclude_accounts:
                        return account
                    return account
        return None


# # Путь к PDF файлу
# pdf_path = "Рахунок за послуги зв'язку GRIS № 084073 від 31.08.2024.pdf"
# # pdf_path = "Рахунок ММ № 207308 від 01.08.2024.pdf"
# # Вызов функции для поиска номеров расчетных счетов
# account = find_account_numbers(pdf_path)
# print(account)
