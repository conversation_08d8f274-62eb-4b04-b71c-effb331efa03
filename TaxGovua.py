# Description: Скрипт для автоматизации работы с сайтом tax.gov.ua
# сохраняет данные о незарегистрированных налоговых накладных в базу данных
# и загружает квитанции в формате PDF в папку проекта

import asyncio
import os
import time
from datetime import datetime, timedelta
from pathlib import Path

import pyautogui
import pyperclip
from dateutil import parser
from selenium import webdriver
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException,
)
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from AsyncPostgresql import async_save_pg
from ChangeKeyBoard import set_keyboard_layout
from tables.t_tax_cabinet import SQL_INSERT

pyautogui.FAILSAFE = False  # Отключаем защиту от выхода курсора за пределы экрана

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()
prefs = {
    "download.prompt_for_download": True,  # Всегда указывать место для скачивания
    "download.directory_upgrade": True,
}
chrome_options.add_experimental_option("prefs", prefs)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Максимизируем окно браузера
driver.maximize_window()


def extract_doc_info(doc_text):
    import re
    match = re.search(r"№ (\d+) від (\d{2}\.\d{2}\.\d{4})", doc_text)
    if match:
        date = parser.parse(match.group(2)).strftime("%Y.%m.%d")
        return f"{match.group(1)} {date}"
    return None


def click_element_by_css_selector(css_selector):
    try:
        element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector))
        )
        element.click()
    except Exception as e:
        print(f"Error clicking element: {e}")


def click_element(xpath):
    try:
        element = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, xpath)))
        element.click()
    except (StaleElementReferenceException, ElementClickInterceptedException):
        element = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, xpath)))
        element.click()


def clik_on_row(row_xpath):
    try:
        row_element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, row_xpath))
        )
        driver.execute_script("arguments[0].click();", row_element)
        time.sleep(1)  # Небольшая задержка для стабильности
    except (StaleElementReferenceException, ElementClickInterceptedException):
        row_element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, row_xpath))
        )
        driver.execute_script("arguments[0].click();", row_element)
        time.sleep(1)  # Небольшая задержка для стабильности


def check_and_click(td_xpath, button_xpath):
    try:
        # Wait for the <td> element to be present
        td_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, td_xpath))
        )
        
        if "РIШЕННЯ" in td_element.text:
            # click_element(button_xpath)
            return True
        
    except Exception as e:
        print(f"Error: {e}")
    return False


# Функция для получения данных с текущей страницы
async def get_table_data(table_xpath):
    rows = driver.find_elements(By.XPATH, table_xpath)
    data_all = []
    for row in rows:
        data = []
        print(row.text)
        cells = row.find_elements(By.TAG_NAME, "td")
        for cell in cells:
            try:
                data.append(cell.text)
                print(cell.text)
            except StaleElementReferenceException:
                # Повторно находим элемент, если он устарел
                cell = driver.find_element(By.XPATH, cell.get_attribute("xpath"))
                data.append(cell.text)
                print(cell.text)
        print("-" * 20)  # Разделитель между строками
        data_all.append(data)
    return data_all


# Функция для перехода на следующую страницу
def go_to_next_page(page_xpath):
    try:
        # Ожидание появления и кликабельности кнопки "Следующая страница"
        next_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, page_xpath))
        )
        if next_button.is_enabled():
            next_button.click()
            return True
    except (TimeoutException, NoSuchElementException):
        print("Кнопка перехода на следующую страницу не найдена или не кликабельна.")
    return False


async def authorizations():
    try:
        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/login")

        # Ожидаем, пока блокирующий элемент исчезнет
        WebDriverWait(driver, 10).until(EC.invisibility_of_element_located((By.CLASS_NAME, "p-blockui-document")))

        # Находим элемент dropdown и кликаем по нему, чтобы открыть список
        click_element("//p-dropdown[@id='selectedCAs111']")

        # Находим элемент с текстом 'КНЕДП ТОВ "Центр сертифікації ключів "Україна"' и кликаем по нему
        click_element('//span[text()=\'КНЕДП ТОВ "Центр сертифікації ключів "Україна"\']')

        el_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[2]/div/div/button/span"
        # Находим кнопку и кликаем по ней
        button = driver.find_element(By.XPATH, el_path)
        button.click()

        # Ожидаем появления окна выбора файла
        time.sleep(2)

        # Используем pyperclip для копирования пути к файлу в буфер обмена и pyautogui для вставки
        cur_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(cur_dir, "41098985_2634316155_DU240119101324.ZS2")

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        # pyautogui.write(file_path)  # некорректно вводит путь к файлу. заменяет символы на другие
        pyautogui.press("enter")

        # Находим поле для ввода пароля и вводим пароль
        psw_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[4]/div/div/input"
        password_input = driver.find_element(By.XPATH, psw_path)
        password_input.send_keys("41098985")

        # Находим кнопку "Зчитати" и кликаем по ней
        read_button = driver.find_element(By.XPATH, "//span[text()='Зчитати']")
        read_button.click()

        # Ожидаем пока прочитается файл
        time.sleep(5)

        # Находим кнопку "Увійти" и кликаем по ней
        login_button = driver.find_element(By.XPATH, "//span[text()='Увійти']")
        login_button.click()
        time.sleep(1)

    except NoSuchElementException as e:
        print(f"страницы закончились: {e}")

    except Exception as e:
        print(e)


# загружает данные с о незарегистрированных налоговых накладных с сайта tax.gov.ua
# и сохраняет их в базу данных
async def main_taxgovua(date_first, date_last):
    try:
        # Авторизация
        await authorizations()

        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/smkor/vat-stop")

        date_input = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//input[@placeholder='з']")))
        date_input.send_keys(Keys.CONTROL + "a")
        date_input.send_keys(Keys.DELETE)
        date_input.send_keys(date_first)

        # Находим поле ввода и устанавливаем значение "01.01.2020"
        date_input = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//input[@placeholder='по']")))
        date_input.send_keys(Keys.CONTROL + "a")
        date_input.send_keys(Keys.DELETE)
        date_input.send_keys(date_last)
        # Находим кнопку "Пошук" и кликаем по ней
        click_element("//span[text()='Пошук']")

        # Ожидаем загрузки страницы с таблицей
        time.sleep(5)

        # Находим элемент заголовка таблицы
        column_header = driver.find_element(By.XPATH, "//th[@psortablecolumn='impdate']")

        # Проверяем, установлена ли сортировка по убыванию
        if column_header.get_attribute("aria-sort") == "descending":
            # Выполняем клик, чтобы изменить сортировку
            column_header.click()

        # Ожидаем загрузки страницы с таблицей
        time.sleep(5)
        table_xpath = "//tbody[@class='p-datatable-tbody']/tr"
        page_xpath = "//button[@class='p-paginator-next p-paginator-element p-link p-ripple']"
        # Получаем данные с первой страницы
        data = await get_table_data(table_xpath)
        await async_save_pg(SQL_INSERT, data)

        # Проходим по всем страницам и собираем данные
        while True:
            if not go_to_next_page(page_xpath):
                break
            time.sleep(5)  # Ожидаем загрузки новой страницы
            data = await get_table_data(table_xpath)
            await async_save_pg(SQL_INSERT, data)
    except Exception as e:
        print(e)

    finally:
        # Закомментируйте или уберите строку закрытия браузера
        driver.quit()

    # Бесконечный цикл, чтобы окно браузера не закрывалось
    # while True:
    #     time.sleep(1)


# нажимает на иконку сохранить и сохраняет файл
def save_receipts(cur_name, doc_name):
    try:
        # Удаляем файл, если есть, перед сохранением
        file_path = os.path.join(cur_name, doc_name)
        if os.path.exists(file_path):
            os.remove(file_path)
            # print(f"Файл {doc_name} удален.")

        # # Нажимаем на кнопку загрузки
        # css_selector = "#centerContent > app-in-view > div.card.ng-star-inserted > div:nth-child(1) > div:nth-child(2) > button"
        # click_element_by_css_selector(css_selector)

        # Нажимаем на кнопку с текстом "XML"
        xml_button = driver.find_element(By.XPATH, "//span[@class='p-button-label' and text()='XML']")
        xml_button.click()

        time.sleep(2)  # Небольшая задержка для появления окна сохранения

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        pyautogui.press("enter")

        # wait_for_file(cur_name, doc_name)

        driver.back()  # Возвращаемся на предыдущую страницу
        pyautogui.press("escape")
    except ElementClickInterceptedException:
        # Делаем окно браузера неактивным на пару секунд и обратно активируем
        pyautogui.hotkey("alt", "tab")  # Переключаемся на другое окно
        time.sleep(1)  # Ждем 2 секунды
        pyautogui.hotkey("alt", "tab")  # Переключаемся обратно на окно браузера
        pyautogui.press("escape")

        # Повторяем попытку клика, если элемент не был кликабельным
        click_element("//span[@class='p-button-icon pi pi-download']")
        time.sleep(2)  # Небольшая задержка для появления окна сохранения

        # Используем pyautogui для взаимодействия с окном сохранения файла
        pyautogui.write(doc_name)
        pyautogui.press("enter")
        time.sleep(2)  # Небольшая задержка для завершения сохранения

        driver.back()  # Возвращаемся на предыдущую страницу

    except Exception as e:
        print(e)
        pass


def chaeck_box_click():
    try:
        # Ищем все чекбоксы с aria-checked="true"
        checkboxes = driver.find_elements(By.XPATH, "//div[@role='checkbox' and @aria-checked='true']")

        # Проходим по всем найденным чекбоксам и кликаем по ним, чтобы установить в false
        for checkbox in checkboxes:
            checkbox.click()

    except NoSuchElementException:
        pass


# Загружает квитанции с сайта tax.gov.ua и сохраняет их в папку проекта в формате PDF
async def download_receipts():
    try:
        await authorizations()

        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/documents/in")

        # Находим кнопку "Квитанції" и кликаем по ней
        # click_element("//span[@class='p-button-label ng-star-inserted' and text()='Квитанції']")
        time.sleep(1)
        click_element("//span[@class='p-button-label ng-star-inserted' and text()='Витяги з реєстрів']")
        time.sleep(5)

        cur_dir = os.path.dirname(os.path.abspath(__file__))

        # Проверяем, установлена ли сортировка по убыванию, если да, то меняем на возрастание
        column_header = driver.find_element(By.XPATH, "//th[@psortablecolumn='dateIn']")
        column_header.click()
        time.sleep(2)
        if column_header.get_attribute("aria-sort") == "ascending":
            # Выполняем клик, чтобы изменить сортировку
            column_header.click()

        table_xpath = "//tbody[@class='p-datatable-tbody']/tr"
        page_xpath = "//button[@class='p-paginator-next p-paginator-element p-link p-ripple']"

        rows = driver.find_elements(By.XPATH, table_xpath)
        for index in range(1, len(rows) + 1):
            try:
                pyautogui.press("escape")

                # Находим все элементы <td> на странице
                td_elements = driver.find_elements(By.TAG_NAME, 'td')
                while not td_elements:
                    td_elements = driver.find_elements(By.TAG_NAME, 'td')

                # Проходим по всем элементам <td> и ищем те, которые содержат "РIШЕННЯ"
                for td in td_elements:
                    try:
                        print(td.text)
                        if "РIШЕННЯ" not in td.text:
                            continue
                        # Кликаем по найденной строке
                        clik_on_row(f"//tbody[@class='p-datatable-tbody']/tr[{index}]")
                        # if index > 1:
                        #     clik_on_row(f"//tbody[@class='p-datatable-tbody']/tr[{index - 1}]")
                        time.sleep(1)  # Небольшая задержка для стабильности

                        # Ждем, пока кнопка с текстом "Перегляд" станет кликабельной
                        try:
                            click_element("//span[@class='p-button-label' and text()='Перегляд']")
                            print("Клик по кнопке 'Перегляд' выполнен.")

                            # Получаем информацию о документе. Будем использовать ее для наименования файла
                            doc_element = driver.find_element(By.XPATH,
                                                              "/html/body/app-root/div/div[2]/div[2]/app-in-view/div[2]/p", )
                            doc_name = doc_element.text
                            doc_name = extract_doc_info(doc_name)
                            # doc_name = os.path.join(cur_dir, f"{doc_name}.pdf")
                            # print(doc_name)

                            # нажимаем сохранить
                            save_receipts(cur_dir, f"{doc_name}.xml")
                            # chaeck_box_click()

                            # time.sleep(5)  # Небольшая задержка для стабильности
                            # driver.back()  # Возвращаемся на предыдущую страницу
                        except Exception as e:
                            print("Кнопка 'Перегляд' не найдена или не кликабельна:", e)
                            pass

                    except StaleElementReferenceException:
                        # Re-find the element and retry
                        td = driver.find_element(By.XPATH, td.get_attribute("xpath"))
                        print(td.text)

                    except Exception as e:
                        # print(e)
                        pass
                # ----------------------------------------------------------------------------------------------------------------------

            except ElementClickInterceptedException:
                # Делаем окно браузера неактивным на пару секунд и обратно активируем
                pyautogui.hotkey('alt', 'tab')  # Переключаемся на другое окно
                time.sleep(1)  # Ждем 2 секунды
                pyautogui.hotkey('alt', 'tab')  # Переключаемся обратно на окно браузера

                # Повторяем попытку клика, если элемент не был кликабельным
                save_receipts(cur_dir, f"{doc_name}.xml")
            except Exception as e:
                print(e)
                pass

        # Проходим по всем страницам и собираем данные
        while True:
            if not go_to_next_page(page_xpath):
                break
            time.sleep(5)  # Ожидаем загрузки новой страницы
            rows = driver.find_elements(By.XPATH, table_xpath)
            for index in range(1, len(rows) + 1):
                clik_on_row(f"//tbody[@class='p-datatable-tbody']/tr[{index}]")
                time.sleep(1)
                if index > 1:
                    clik_on_row(f"//tbody[@class='p-datatable-tbody']/tr[{index - 1}]")
                # click_element("//span[@class='p-button-label' and text()='Перегляд']")
                td_xpath = "//td[contains(text(), 'РIШЕННЯ')]"
                button_xpath = "//span[@class='p-button-label' and text()='Перегляд']"
                result = check_and_click(td_xpath, button_xpath)
                if not result:
                    continue
                time.sleep(2)  # Небольшая задержка для стабильности

                # Получаем информацию о документе. Будем использовать ее для наименования файла
                doc_element = driver.find_element(By.XPATH,
                                                  "/html/body/app-root/div/div[2]/div[2]/app-in-view/div[2]/p")
                doc_name = doc_element.text
                doc_name = extract_doc_info(doc_name)
                doc_name = doc_name.replace("  ", " ")
                doc_name = os.path.join(cur_dir, f"{doc_name}.xml")
                print(f"Документ: {doc_name}.xml")

                try:
                    save_receipts(cur_dir, f"{doc_name}.xml")
                except ElementClickInterceptedException:
                    # Делаем окно браузера неактивным на пару секунд и обратно активируем
                    pyautogui.hotkey('alt', 'tab')  # Переключаемся на другое окно
                    time.sleep(2)  # Ждем 2 секунды
                    pyautogui.hotkey('alt', 'tab')  # Переключаемся обратно на окно браузера

                    save_receipts(cur_dir, f"{doc_name}.xml")

    except Exception as e:
        print(e)

    finally:
        # Закомментируйте или уберите строку закрытия браузера
        # driver.quit()
        pass

    # Бесконечный цикл, чтобы окно браузера не закрывалось
    while True:
        time.sleep(1)


if __name__ == "__main__":
    date_last = datetime.now().strftime("%d.%m.%Y")
    date_first = (datetime.now() - timedelta(days=500)).strftime("%d.%m.%Y")
    asyncio.run(download_receipts())
    # asyncio.run(main_taxgovua(date_first, date_last))
