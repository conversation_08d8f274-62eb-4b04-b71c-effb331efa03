# Description: Скрипт для автоматизации работы с сайтом https://cabinet.tax.gov.ua/tax-invoices/written
# сохраняет данные о налоговых накладных в базу данных, без фильрации по статусу документа
# без сохранения файлов, только данные о них
import asyncio
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
import pyautogui
import pyperclip
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.common.exceptions import (
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException,
    TimeoutException,
)
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from AsyncPostgresql import async_save_pg
from ChangeKeyBoard import set_keyboard_layout
from tables.t_tax_cabinet_all import SQL_DELETE_DOUBLE, SQL_INSERT_TO_T_TAX_CABINET_ALL

pyautogui.FAILSAFE = False  # Отключаем защиту от выхода курсора за пределы экрана

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()
prefs = {
    "download.prompt_for_download": True,  # Всегда указывать место для скачивания
    "download.directory_upgrade": True,
}
chrome_options.add_experimental_option("prefs", prefs)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Максимизируем окно браузера
driver.maximize_window()


# Функция для клика по элементу с использованием XPath
def click_element_by_xpath(xpath):
    try:
        # Ожидание появления и кликабельности элемента
        element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        element.click()
    except TimeoutException:
        print(f"TimeoutException: Элемент с XPath {xpath} не кликабелен.")
    except NoSuchElementException:
        print(f"NoSuchElementException: Элемент с XPath {xpath} не найден.")
    except ElementClickInterceptedException:
        print(
            f"ElementClickInterceptedException: Элемент с XPath {xpath} перекрыт другим элементом. Выполняем клик с помощью JavaScript."
        )
        driver.execute_script("arguments[0].click();", element)
    except StaleElementReferenceException:
        print(
            f"StaleElementReferenceException: Элемент с XPath {xpath} устарел. Повторный поиск элемента."
        )
        click_element_by_xpath(xpath)  # Повторный вызов функции для поиска элемента
    except Exception as e:
        print(f"Error: {e}")


# Функция для получения данных с текущей страницы
async def get_table_data(table_xpath):
    rows = driver.find_elements(By.XPATH, table_xpath)
    data_all = []
    for row in rows:
        data = []
        cells = row.find_elements(By.TAG_NAME, "td")
        for cell in cells[:-1]:  # Проходимся по всем элементам, кроме последнего
            try:
                data.append(cell.text)
                print(f"cells'{cell.text}'")
            except StaleElementReferenceException:
                # Повторно находим элемент, если он устарел
                cell = driver.find_element(By.XPATH, cell.get_attribute("xpath"))
                data.append(cell.text)
                print(cell.text)
        print("-" * 20)  # Разделитель между строками
        if data:
            data_all.append(data)

    time.sleep(1)  # Небольшая задержка для стабильности
    return data_all


# Функция для перехода на следующую страницу
def go_to_next_page(page_xpath):
    try:
        # Ожидание появления и кликабельности кнопки "Следующая страница"
        next_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, page_xpath)))
        if next_button.is_enabled():
            try:
                next_button.click()
            except ElementClickInterceptedException:
                print(
                    "ElementClickInterceptedException: Кнопка перекрыта другим элементом. Выполняем клик с помощью JavaScript."
                )
                driver.execute_script("arguments[0].click();", next_button)
            return True
    except NoSuchElementException:
        print(
            "NoSuchElementException: Кнопка перехода на следующую страницу не найдена."
        )
    except TimeoutException:
        print("TimeoutException: Кнопка перехода на следующую страницу не кликабельна.")
    except StaleElementReferenceException:
        print(
            "StaleElementReferenceException: Элемент устарел. Повторный поиск элемента."
        )
        return go_to_next_page(
            page_xpath
        )  # Повторный вызов функции для поиска элемента
    except Exception as e:
        print(f"Error: {e}")

    return False


async def authorizations():
    try:
        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/login")

        # Ожидаем, пока блокирующий элемент исчезнет
        WebDriverWait(driver, 10).until(
            EC.invisibility_of_element_located((By.CLASS_NAME, "p-blockui-document"))
        )

        # Находим элемент dropdown и кликаем по нему, чтобы открыть список
        click_element_by_xpath("//p-dropdown[@id='selectedCAs111']")

        # Находим элемент с текстом 'КНЕДП ТОВ "Центр сертифікації ключів "Україна"' и кликаем по нему
        click_element_by_xpath(
            '//span[text()=\'КНЕДП ТОВ "Центр сертифікації ключів "Україна"\']'
        )

        el_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[2]/div/div/button/span"
        # Находим кнопку и кликаем по ней
        button = driver.find_element(By.XPATH, el_path)
        button.click()

        # Ожидаем появления окна выбора файла
        time.sleep(2)

        # Используем pyperclip для копирования пути к файлу в буфер обмена и pyautogui для вставки
        cur_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(cur_dir, "41098985_2634316155_DU240119101324.ZS2")

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        # pyautogui.write(file_path)  # некорректно вводит путь к файлу. заменяет символы на другие
        pyautogui.press("enter")

        # Находим поле для ввода пароля и вводим пароль
        psw_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[4]/div/div/input"
        password_input = driver.find_element(By.XPATH, psw_path)
        password_input.send_keys("41098985")

        # Находим кнопку "Зчитати" и кликаем по ней
        read_button = driver.find_element(By.XPATH, "//span[text()='Зчитати']")
        read_button.click()

        # Ожидаем пока прочитается файл
        time.sleep(5)

        # Находим кнопку "Увійти" и кликаем по ней
        login_button = driver.find_element(By.XPATH, "//span[text()='Увійти']")
        login_button.click()
        time.sleep(1)

    except NoSuchElementException as e:
        print(f"страницы закончились: {e}")

    except Exception as e:
        print(e)


# загружает данные о налоговых накладных с https://cabinet.tax.gov.ua/tax-invoices/written
# и сохраняет их в базу данных
async def main_taxgovua(date_first, date_last):
    try:

        # Открываем сайт
        # driver.get("https://cabinet.tax.gov.ua/smkor/vat-stop")
        driver.get("https://cabinet.tax.gov.ua/tax-invoices/written")

        date_input = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//input[@placeholder='з']"))
        )
        date_input.send_keys(Keys.CONTROL + "a")
        date_input.send_keys(Keys.DELETE)
        date_input.send_keys(date_first)

        # Находим поле ввода и устанавливаем значение "01.01.2020"
        date_input = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//input[@placeholder='по']"))
        )
        date_input.send_keys(Keys.CONTROL + "a")
        date_input.send_keys(Keys.DELETE)
        date_input.send_keys(date_last)

        # Находим кнопку "Пошук" и кликаем по ней
        click_element_by_xpath("//span[text()='Пошук']")

        # Ожидаем загрузки страницы с таблицей
        time.sleep(5)
        table_xpath = "//tbody[@class='p-datatable-tbody']/tr"
        page_xpath = (
            "//button[@class='p-paginator-next p-paginator-element p-link p-ripple']"
        )
        # Получаем данные с первой страницы
        data = await get_table_data(table_xpath)
        if data:
            # await async_save_pg(SQL_INSERT, data)
            await async_save_pg(SQL_INSERT_TO_T_TAX_CABINET_ALL, data)

        # Проходим по всем страницам и собираем данные
        while True:
            if not go_to_next_page(page_xpath):
                break
            time.sleep(5)  # Ожидаем загрузки новой страницы
            data = await get_table_data(table_xpath)
            if data:
                # await async_save_pg(SQL_INSERT, data)
                await async_save_pg(SQL_INSERT_TO_T_TAX_CABINET_ALL, data)

        return True

    except Exception as e:
        print(e)

    return False


async def cycle_month(date_first):

    date_first = parser.parse(date_first, dayfirst=True)

    # Авторизация
    await authorizations()

    while date_first < datetime.now():
        date_last = date_first + relativedelta(months=1) - timedelta(days=1)
        date_first = date_first.strftime("%d.%m.%Y")
        date_last = date_last.strftime("%d.%m.%Y")
        result = await main_taxgovua(date_first, date_last)
        if result:
            date_first = parser.parse(date_last, dayfirst=True) + timedelta(days=1)
        else:
            break
        
    await async_save_pg(SQL_DELETE_DOUBLE)


if __name__ == "__main__":
    date_first = "01.08.2021"  # "01.06.2024" уже сделал, "01.01.2024" - PyCharm
    asyncio.run(cycle_month(date_first))
