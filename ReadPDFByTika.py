import os
from tika import parser

# Укажите путь к Java в переменной окружения PATH
os.environ['JAVA_HOME'] = r'c:\Program Files\Java\jdk-17'
os.environ['PATH'] = os.environ['JAVA_HOME'] + r'\bin;' + os.environ['PATH']


def extract_text_from_pdf(pdf_path):
    # Parse the PDF file
    raw = parser.from_file(pdf_path)
    # Extract the content
    text = raw['content']
    return text


if __name__ == '__main__':
    pdf_path = r'D:\Prestige\Python\Medoc\03180041098985J1201016100000012310920240318 (1).pdf'
    extracted_text = extract_text_from_pdf(pdf_path)
    print(extracted_text)
