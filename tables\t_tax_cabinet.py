# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet"
SQL_CREATE_TABLE = f"""
-- DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    id uuid DEFAULT uuid_generate_v4 (),
    register_number numeric(13) NOT NULL,
    register_date date NOT NULL,
    doc_type varchar(10) NOT NULL,
    doc_date date NOT NULL,
    doc_number varchar(20) NOT NULL,
    amount_vat numeric(13, 2) NOT NULL,
    doc_status varchar(100) NOT NULL,
    CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
    CONSTRAINT {TABLE_NAME}_unique UNIQUE (register_number, doc_number)
  );
  
  COMMENT ON TABLE {TABLE_NAME} IS 'НН, РК регистриция которых приостановлена';
  COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
  COMMENT ON COLUMN {TABLE_NAME}.register_number IS 'Реєстраційний номер';
  COMMENT ON COLUMN {TABLE_NAME}.register_date IS 'Дата реєстрації';
  COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип документа';
  COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата документа';
  COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер документа';
  COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'Сума ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.doc_status IS 'Статус документа';  
"""

SQL_INSERT = f"""
  INSERT INTO {TABLE_NAME} (
      register_number, register_date, doc_type, doc_date, doc_number, amount_vat, doc_status)
  VALUES ($1, to_date($2,'dd.mm.yyyy'), $3, to_date($4,'dd.mm.yyyy'), $5, REPLACE(REPLACE($6,' ',''),',','.')::NUMERIC(13,2), $7)
  ON CONFLICT (register_number, doc_number)
  DO UPDATE SET
      register_date = EXCLUDED.register_date,
      doc_type = EXCLUDED.doc_type,
      doc_date = EXCLUDED.doc_date,
      amount_vat = EXCLUDED.amount_vat,
      doc_status = EXCLUDED.doc_status
  ;
"""


async def main_tax_cabinet():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_tax_cabinet())
