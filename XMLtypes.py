# Определение функций для преобразования значений в строковый формат
def to_str(x):
    return str(x)


xml_types = {
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "HNUMREG",
        "HDATE1",
        "HNUM2",
        "HDATE2",
        "R01G1D",
        "R01G21",
        "R01G4S",
        "R01G5",
        "R01G6",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2S",
        "R03G3S",
        "R03G4D",
        "R03G5D",
        "M01",
        "HEXECUTOR",
    ): "type1",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "HNUMREG",
        "HDATE1",
        "HNUM2",
        "HDATE2",
        "R01G1D",
        "R01G21",
        "R01G4S",
        "R01G5",
        "R01G6",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2S",
        "R03G3S",
        "R03G4D",
        "R03G5D",
        "M02",
        "T1RXXXXG1S",
        "HEXECUTOR",
    ): "type2",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "HTIN",
        "HNPDV",
        "HNAME",
        "M02",
        "R02G1D",
        "R02G2S",
        "M04",
        "HEXECUTOR",
    ): "type3",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "HTIN",
        "HNPDV",
        "HNAME",
        "R01G1S",
        "R01G2D",
        "R01G3S",
        "R02G1D",
        "R02G2S",
        "M021",
        "HEXECUTOR",
    ): "type4",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "M0200",
        "M0220",
        "M0223",
        "HEXECUTOR",
    ): "type5",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0100",
        "HEXECUTOR",
    ): "type6",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0100",
        "HSTI",
        "HEXECUTOR",
    ): "type7",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0200",
        "M0220",
        "M0221",
        "M0223",
        "M0224",
        "M0225",
        "R0220G1S",
        "HEXECUTOR",
    ): "type8",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0200",
        "M0220",
        "M0223",
        "HEXECUTOR",
    ): "type9",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0200",
        "M0220",
        "M0223",
        "M0224",
        "M0225",
        "R0220G1S",
        "HEXECUTOR",
    ): "type10",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0200",
        "M0220",
        "M0223",
        "M0224",
        "R0220G1S",
        "HEXECUTOR",
    ): "type11",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0200",
        "M0220",
        "M0223",
        "R0220G1S",
        "HEXECUTOR",
    ): "type12",
    (
        "TIN",
        "C_DOC",
        "C_DOC_SUB",
        "C_DOC_VER",
        "C_DOC_TYPE",
        "C_DOC_CNT",
        "C_REG",
        "C_RAJ",
        "PERIOD_MONTH",
        "PERIOD_TYPE",
        "PERIOD_YEAR",
        "C_STI_ORIG",
        "C_DOC_STAN",
        "D_FILL",
        "SOFTWARE",
        "HDATE",
        "HNUM",
        "R01G1D",
        "R01G21",
        "R01G3S",
        "R01G4",
        "R01G5",
        "R02G1S",
        "R02G2",
        "R02G3S",
        "R02G4D",
        "R03G1S",
        "R03G2",
        "R03G3S",
        "R03G4D",
        "M0220",
        "M0221",
        "M0223",
        "M02231",
        "M022311",
        "M022313",
        "M022316",
        "M02234",
        "M02235",
        "M0224",
        "M0226",
        "R0220G1S",
        "HSTI",
        "HEXECUTOR",
    ): "type13",
}

type1_rest = ["HDATE",
              "HNUM",
              "HNUMREG",
              "HDATE1",
              "HNUM2",
              "HDATE2",
              "R01G1D",
              "R01G21",
              "R01G4S",
              "R01G5",
              "R01G6",
              "R02G1S",
              "R02G2",
              "R02G3S",
              "R03G1S",
              "R03G2S",
              "R03G3S",
              ]
type1_columns = {"HDATE": "resolutions_date",
                 "HNUM": "resolutions_number",
                 "HNUMREG": "reg_num",
                 "HDATE1": "reg_date",
                 "HNUM2": "resolutions_number2",
                 "HDATE2": "resolutions_date2",
                 "R01G1D": "doc_date",
                 "R01G21": "doc_number",
                 "R01G4S": "doc_type",
                 "R01G5": "total_amount",
                 "R01G6": "tax_amount",
                 "R02G1S": "supplier_okpo",
                 "R02G2": "supplier_ipn",
                 "R02G3S": "supplier_name",
                 "R03G1S": "customer_okpo",
                 "R03G2S": "customer_ipn",
                 "R03G3S": "customer_name"
                 }

type2_rest = [
    "HDATE",
    "HNUM",
    "HNUMREG",
    "HDATE1",
    "HNUM2",
    "HDATE2",
    "R01G1D",
    "R01G21",
    "R01G4S",
    "R01G5",
    "R01G6",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2S",
    "R03G3S",
    'T1RXXXXG1S',
]
type2_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "HNUMREG": "reg_num",
    "HDATE1": "reg_date",
    "HNUM2": "resolutions_number2",
    "HDATE2": "resolutions_date2",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G4S": "doc_type",
    "R01G5": "total_amount",
    "R01G6": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2S": "customer_ipn",
    "R03G3S": "customer_name",
    'T1RXXXXG1S': "description",

}

# пропускаем
type3_rest = []

# найти в pdf 7864604/41098985
type4_rest = []

type5_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
]
type5_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
}

type6_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
    "R03G3S",
]
type6_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
}

type7_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
    "R03G3S",
]
type7_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
}

type8_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
    "R03G3S",
    "R0220G1S",
]
type8_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
    "R0220G1S": "description",
}

type9_rest = ["HDATE",
              "HNUM",
              "R01G1D",
              "R01G21",
              "R01G3S",
              "R01G4",
              "R01G5",
              "R02G1S",
              "R02G2",
              "R02G3S",
              "R03G1S",
              "R03G2",
              "R03G3S",
              ]
type9_columns = {"HDATE": "resolutions_date",
                 "HNUM": "resolutions_number",
                 "R01G1D": "doc_date",
                 "R01G21": "doc_number",
                 "R01G3S": "doc_type",
                 "R01G4": "total_amount",
                 "R01G5": "tax_amount",
                 "R02G1S": "supplier_okpo",
                 "R02G2": "supplier_ipn",
                 "R02G3S": "supplier_name",
                 "R03G1S": "customer_okpo",
                 "R03G2": "customer_ipn",
                 "R03G3S": "customer_name",
                 }

type10_rest = ["HDATE",
               "HNUM",
               "R01G1D",
               "R01G21",
               "R01G3S",
               "R01G4",
               "R01G5",
               "R02G1S",
               "R02G2",
               "R02G3S",
               "R03G1S",
               "R03G2",
               "R03G3S",
               "R0220G1S",
               ]
type10_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
    "R0220G1S": "description",
}

type11_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
    "R03G3S",
    "R0220G1S",
]
type11_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
    "R0220G1S": "description",
}

type12_rest = [
    "HDATE",
    "HNUM",
    "R01G1D",
    "R01G21",
    "R01G3S",
    "R01G4",
    "R01G5",
    "R02G1S",
    "R02G2",
    "R02G3S",
    "R03G1S",
    "R03G2",
    "R03G3S",
    "R0220G1S",
]
type12_columns = {
    "HDATE": "resolutions_date",
    "HNUM": "resolutions_number",
    "R01G1D": "doc_date",
    "R01G21": "doc_number",
    "R01G3S": "doc_type",
    "R01G4": "total_amount",
    "R01G5": "tax_amount",
    "R02G1S": "supplier_okpo",
    "R02G2": "supplier_ipn",
    "R02G3S": "supplier_name",
    "R03G1S": "customer_okpo",
    "R03G2": "customer_ipn",
    "R03G3S": "customer_name",
    "R0220G1S": "description",
}

type13_rest = ["HDATE",
               "HNUM",
               "R01G1D",
               "R01G21",
               "R01G3S",
               "R01G4",
               "R01G5",
               "R02G1S",
               "R02G2",
               "R02G3S",
               "R03G1S",
               "R03G2",
               "R03G3S",
               "R0220G1S",
               ]
type13_columns = {"HDATE": "resolutions_date",
                  "HNUM": "resolutions_number",
                  "R01G1D": "doc_date",
                  "R01G21": "doc_number",
                  "R01G3S": "doc_type",
                  "R01G4": "total_amount",
                  "R01G5": "tax_amount",
                  "R02G1S": "supplier_okpo",
                  "R02G2": "supplier_ipn",
                  "R02G3S": "supplier_name",
                  "R03G1S": "customer_okpo",
                  "R03G2": "customer_ipn",
                  "R03G3S": "customer_name",
                  "R0220G1S": "description",
                  }


def get_type_rest(xml_type):
    return globals().get(f"{xml_type}_rest")


def get_type_columns(xml_type):
    return globals().get(f"{xml_type}_columns")


def find_type_by_columns(df):
    for columns, xml_type in xml_types.items():
        if set(columns) == set(df.columns):
            return xml_type
    return None
