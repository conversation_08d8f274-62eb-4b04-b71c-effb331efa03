# таблица для хранения данных из сайта https://cabinet.tax.gov.ua/tax-invoices/written (все выданные НН, РК)

import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_erpn_api"
SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code int8 NULL,
        prdinn int8 NULL, -- ИНН продавца
        pkpinn int8 NULL, -- ИНН покупателя
        nmr numeric NULL, -- номер НН/РК
        crtdate date NULL, -- дата НН/РК
        ndssm float8 NULL, -- НДС
        r4100g11 numeric(15,2) NULL, -- сумма
        tin int8 NULL, -- О<PERSON><PERSON><PERSON> продавца
        cptin int8 NULL, -- ОКПО покупателя
        ijcode int8 NULL,
        crcode int8 NULL,  -- code original
        corrnmr numeric NULL, -- номер НН ориг
        flags int8 NULL,
        dend date NULL,
        ftype int8 NULL, -- 0-НН/1-РК
        htypr int8 NULL,
        waitcorr int8 NULL,
        waitrisk int8 NULL,
        impdate date NULL, -- дата квитанции
        hsmcstt int8 NULL, -- тип статуса
        hsmcsttname varchar(200) NULL, -- статус
        docrnn text NULL, -- регНомер
        hnamesel text NULL, -- продавец
        hnamebuy text NULL, -- покупатель
        kvt2 int8 NULL,
        kvt3 int8 NULL,
        kvt4 int8 NULL,
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (docrnn, cptin, impdate)  -- регНомер, ОКПО покупателя, регДата
    );
      
        COMMENT ON TABLE {TABLE_NAME} IS 'НН, РК полученные чз api';
        COMMENT ON COLUMN {TABLE_NAME}.prdinn IS 'ИНН продавца';
        COMMENT ON COLUMN {TABLE_NAME}.pkpinn IS 'ИНН покупателя';
        COMMENT ON COLUMN {TABLE_NAME}.nmr IS 'номер НН/РК';
        COMMENT ON COLUMN {TABLE_NAME}.crtdate IS 'дата НН/РК';
        COMMENT ON COLUMN {TABLE_NAME}.ndssm IS 'НДС';
        COMMENT ON COLUMN {TABLE_NAME}.r4100g11 IS 'сумма';
        COMMENT ON COLUMN {TABLE_NAME}.tin IS 'ОКПО продавца';
        COMMENT ON COLUMN {TABLE_NAME}.cptin IS 'ОКПО покупателя';
        COMMENT ON COLUMN {TABLE_NAME}.corrnmr IS 'номер НН ориг';
        COMMENT ON COLUMN {TABLE_NAME}.ftype IS '0-НН/1-РК';
        COMMENT ON COLUMN {TABLE_NAME}.impdate IS 'дата квитанции';
        COMMENT ON COLUMN {TABLE_NAME}.hsmcstt IS 'тип статуса';
        COMMENT ON COLUMN {TABLE_NAME}.hsmcsttname IS 'статус';
        COMMENT ON COLUMN {TABLE_NAME}.docrnn IS 'регНомер';
        COMMENT ON COLUMN {TABLE_NAME}.hnamesel IS 'продавец';
        COMMENT ON COLUMN {TABLE_NAME}.hnamebuy IS 'покупатель';
        COMMENT ON COLUMN {TABLE_NAME}.crcode IS 'code original';
        COMMENT ON COLUMN {TABLE_NAME}.docdate IS 'регДата формат даты';
"""

SQL_INSERT_ERPN = f"""
	INSERT INTO {TABLE_NAME} 
        (code, prdinn, pkpinn, nmr, crtdate, ndssm, r4100g11, tin, cptin, ijcode, 
        crcode, corrnmr, flags, dend, ftype, htypr, waitcorr, waitrisk, impdate, hsmcstt,
        hsmcsttname, docrnn, hnamesel, hnamebuy, kvt2, kvt3, kvt4)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, 
	    $24, $25, $26, $27)
	ON CONFLICT (docrnn, cptin, impdate)
	DO UPDATE SET
	    code = EXCLUDED.code,
	    ijcode = EXCLUDED.ijcode,
        prdinn = EXCLUDED.prdinn,
        pkpinn = EXCLUDED.pkpinn,
        nmr = EXCLUDED.nmr,
        crtdate = EXCLUDED.crtdate,
        ndssm = EXCLUDED.ndssm,
        r4100g11 = EXCLUDED.r4100g11,
        tin = EXCLUDED.tin,
--        cptin = EXCLUDED.cptin,
        crcode = EXCLUDED.crcode,
        corrnmr = EXCLUDED.corrnmr,
        flags = EXCLUDED.flags,
        dend = EXCLUDED.dend,
        ftype = EXCLUDED.ftype,
        htypr = EXCLUDED.htypr,
        waitcorr = EXCLUDED.waitcorr,
        waitrisk = EXCLUDED.waitrisk,
        impdate = EXCLUDED.impdate,
        hsmcstt = EXCLUDED.hsmcstt,
        hsmcsttname = EXCLUDED.hsmcsttname,
--        docrnn = EXCLUDED.docrnn,
        hnamesel = EXCLUDED.hnamesel,
        hnamebuy = EXCLUDED.hnamebuy,
        kvt2 = EXCLUDED.kvt2,
        kvt3 = EXCLUDED.kvt3,
        kvt4 = EXCLUDED.kvt4
  ;
"""

SQL_CREATE_INDEX = f"""
    CREATE INDEX idx_{TABLE_NAME} ON {TABLE_NAME} (cptin, crtdate DESC);
"""

async def main_t_tax_cabinet_erpn_api_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")
    result = await async_save_pg(SQL_CREATE_INDEX)
    print(f"{result}, index for {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_erpn_api_async())
