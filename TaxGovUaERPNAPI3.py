import asyncio
import time
from datetime import datetime

import aiohttp
import requests  # вначале установите библиотеку grequests, потом requests
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta

from AsyncPostgresql import async_save_pg
from TaxGovUaConfig import get_token, remove_duplicates
from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API
from ChromeDriverUpdater import update_chromedriver_if_needed


# Обновляем ChromeDriver, если это необходимо
update_chromedriver_if_needed()

url_const = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="

driver, token = get_token()
wrong_urls = []


# Создаем url с датой начала и конца периода. Нужно для получения количества страниц
async def get_pages_urls(date_in, date_out):
    date_in = parse(date_in, dayfirst=True).date()
    date_out = parse(date_out, dayfirst=True).date()
    interval_days = (date_out - date_in).days
    return [
        (
            f"{url_const}{(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2000:00:00"
            f"&toImpdate={(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2023:59:59"
            "&sort=impdate"
        )
        for i in range(interval_days + 1)
    ]


# получение количества страниц в данном периоде
async def get_page_count_async(url, session, semaphore, retries=3):
    global url_const, token, wrong_urls, driver
    async with semaphore:
        await asyncio.sleep(5)
        for attempt in range(retries):
            print(f"{datetime.now()}; url: {url}")
            if not token:
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token()
                print(f"Update token: {datetime.now()}; {token}")
            headers = {"Authorization": token, "Content-Type": "application/json"}
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        result = await response.json()
                        return [url, result.get("totalPages")]
            except aiohttp.ClientError as e:
                print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                # print(f"Тайм-аут при запросе данных: {url}")
                pass
            await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
            _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return [0, 0]


async def get_page_count(url, retries=3):
    global url_const, driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                return [url, result.get("totalPages")]
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    print(f"ERROR page url: {url}\n{response.text}")
    wrong_urls.append(url)
    return [0, 0]


# Асинхронная функция для выполнения HTTP-запроса
async def fetch(url, retries=3):
    global driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                return result
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    # print(f"count wrong_urls: {len(wrong_urls)}; url: {url}")
    print(f"ERROR url: {url}\n{response.text}")
    wrong_urls.append(url)
    return None


# отбираем url с количеством страниц, у которых количество страниц больше 0
async def create_url_with_pages(urls_pages):
    return [[url, pages] for url, pages in urls_pages if pages > 0]


# Создаем url для периода с номером страницы.
# Количество url = количество страниц
async def create_urls(url_pages):
    all_urls = []
    for url, page_number in url_pages:
        urls = [f"{url}&page={page}" for page in range(page_number)]
        all_urls.extend(urls)
    return all_urls


# Асинхронная функция для выполнения HTTP-запроса
async def fetch_async(url, session, semaphore, retries=3):
    global driver, token, wrong_urls
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        return await response.json()
            except Exception as e:
                await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
                _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return None


# Асинхронная функция для запуска задач по 5 штук одновременно
async def run_tasks(urls, pages=False):
    semaphore = asyncio.Semaphore(5)  # Ограничение на 5 одновременных задач
    async with aiohttp.ClientSession() as session:
        if not pages:
            # tasks = [fetch_async(url, session, semaphore) for url in urls]
            tasks = [fetch(url) for url in urls]
        else:
            # tasks = [get_page_count_async(url, session, semaphore) for url in urls]
            tasks = [get_page_count(url) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


# получаем content из json
async def get_content_from_response(responses):
    contents = [r.get("content") for r in responses if r]
    return [data_dict.values() for data_list in contents for data_dict in data_list]


# Преобразование данных в нужный формат
async def convert_data(data):
    data[3] = int(data[3]) if data[3] else 0  # Преобразование nmr в int
    data[11] = int(data[11]) if data[11] else 0  # Преобразование corrnmr в int
    data[4] = parse(data[4]).date() if data[4] else data[4]
    data[13] = parse(data[13]).date() if data[13] else data[13]
    data[18] = parse(data[18]).date() if data[18] else data[18]
    return tuple(data)


async def main(date_from, date_to):
    global wrong_urls, driver, token
    urls = await get_pages_urls(date_from, date_to)
    urls_pages = await run_tasks(urls, pages=True)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL страниц: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        urls_pages.extend(await run_tasks(urls, pages=True))

    urls_pages_greater_0 = await create_url_with_pages(urls_pages)
    urls = await create_urls(urls_pages_greater_0)
    responses = await run_tasks(urls)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        responses.extend(await run_tasks(urls))

    # удаляем пустые значения из списка
    responses = [r for r in responses if r]
    datas = await get_content_from_response(responses)
    tasks = [convert_data(list(data)) for data in datas]
    data = await asyncio.gather(*tasks)
    result = await async_save_pg(SQL_INSERT_ERPN, data)
    print(f"Data saved: {result}")

    result = await async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API)
    print(f"Data saved to t_tax_cabinet_erpn_api_block: {result}")

    result = remove_duplicates("t_tax_cabinet_erpn_api")
    print(f"Remove duplicates: {result}")


if __name__ == "__main__":
    print("Start", datetime.now())
    date_to = datetime.now().strftime("%Y-%m-%d")
    date_from = (datetime.now() - relativedelta(months=24)).replace(day=1).strftime("%Y-%m-%d")
    asyncio.run(main(date_from, date_to))
    driver.quit()
    print("End", datetime.now())
