# Description: Скрипт для автоматизации работы с сайтом https://cabinet.tax.gov.ua/documents/in
# сохраняет Решения в формате XML и pdf в папку Downloads

import asyncio
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
import pyautogui
import pyperclip
from dateutil import parser
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.common.exceptions import (
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException,
    TimeoutException,
)
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from AsyncPostgresql import async_save_pg
from ChangeKeyBoard import set_keyboard_layout
from tables.t_tax_cabinet_all import SQL_INSERT_TO_T_TAX_CABINET_ALL

pyautogui.FAILSAFE = False  # Отключаем защиту от выхода курсора за пределы экрана

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()
prefs = {
    "download.prompt_for_download": True,  # Всегда указывать место для скачивания
    "download.directory_upgrade": True,
}
# prefs = {
#     "profile.default_content_settings.popups": 0,  # Отключение всплывающих окон
#     "download.prompt_for_download": False,  # Отключение запроса на загрузку
#     "download.default_directory": os.path.join(cur_dir, "Downloads"),  # Путь для сохранения файлов
#     "profile.content_settings.exceptions.automatic_downloads.*.setting": 1,  # Разрешить автоматические загрузки без запроса подтверждения
# }
chrome_options.add_experimental_option("prefs", prefs)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")



# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Максимизируем окно браузера
driver.maximize_window()


def extract_doc_info(doc_text):
    import re

    match = re.search(r"№ (\d+) від (\d{2}\.\d{2}\.\d{4})", doc_text)
    if match:
        date = parser.parse(match.group(2)).strftime("%Y.%m.%d")
        return f"{match.group(1)} {date}"
    return None


# Функция для клика по элементу с использованием XPath
def click_element_by_xpath(xpath):
    try:
        # Ожидание появления и кликабельности элемента
        element = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        element.click()
    except TimeoutException:
        print(f"TimeoutException: Элемент с XPath {xpath} не кликабелен.")
    except NoSuchElementException:
        print(f"NoSuchElementException: Элемент с XPath {xpath} не найден.")
    except ElementClickInterceptedException:
        print(
            f"ElementClickInterceptedException: Элемент с XPath {xpath} перекрыт другим элементом. Выполняем клик с помощью JavaScript."
        )
        driver.execute_script("arguments[0].click();", element)
    except StaleElementReferenceException:
        print(
            f"StaleElementReferenceException: Элемент с XPath {xpath} устарел. Повторный поиск элемента."
        )
        click_element_by_xpath(xpath)  # Повторный вызов функции для поиска элемента
    except Exception as e:
        print(f"Error: {e}")


def save_receipts(cur_name, doc_name):
    try:
        # Удаляем файл, если есть, перед сохранением
        file_path = os.path.join(cur_name, f"{doc_name}")
        if os.path.exists(file_path):
            os.remove(file_path)

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)
        print(f"file_path: {file_path}")
        time.sleep(1)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        time.sleep(1)
        pyautogui.press("enter")
        time.sleep(1)

    except Exception as e:
        print(e)


def find_and_save_receipts(cell):
    if "РIШЕННЯ" in cell.text.upper():
        cell.click()
        cur_dir = os.path.dirname(os.path.abspath(__file__))

        # Находим кнопку "Перегляд" и кликаем по ней
        click_element_by_xpath("//span[@class='p-button-label' and text()='Перегляд']")

        time.sleep(1)

        # на всякий случай закрываем окно если не закрылось с предыдущего раза
        pyautogui.press("escape")

        # Получаем информацию о документе. Будем использовать ее для наименования файла
        element = driver.find_element(
            By.XPATH,
            "/html/body/app-root/div/div[2]/div[2]/app-in-view/div[2]/p",
        )

        # Извлекаем информацию об имени документе
        doc_info = extract_doc_info(element.text)

        # Находим кнопку "XML" и кликаем по ней
        click_element_by_xpath("//span[@class='p-button-label' and text()='XML']")
        save_receipts(cur_dir, f"{doc_info}.xml")

        # Находим кнопку "PDF" и кликаем по ней
        click_element_by_xpath('//*[@id="centerContent"]/app-in-view/cb-pdf-view/div[1]/div/div/div[2]/button/span[1]')
        save_receipts(cur_dir, f"{doc_info}.pdf")

        # Возвращаемся на предыдущую страницу, в случае если мы находимся на странице с документом
        driver.back()


def get_table_data(table_xpath):
    # Ожидание, пока элементы таблицы станут видимыми
    wait = WebDriverWait(driver, 10)

    while True:
        try:
            # Находим строки таблицы по XPATH с ожиданием, пока они станут доступными
            rows = wait.until(
                EC.presence_of_all_elements_located((By.XPATH, table_xpath))
            )

            for i in range(len(rows)):
                # Нужно снова искать элементы после каждой итерации, т.к. элементы могут устареть
                rows = wait.until(
                    EC.presence_of_all_elements_located((By.XPATH, table_xpath))
                )
                row = rows[i]

                # Находим все ячейки в строке
                cells = row.find_elements(By.TAG_NAME, "td")

                for cell in cells:
                    try:
                        # Ваш метод обработки данных из ячейки
                        find_and_save_receipts(cell)
                    except StaleElementReferenceException:
                        # Повторно находим ячейки в строке, если элемент устарел
                        print("Элемент устарел, повторный поиск")
                        rows = wait.until(
                            EC.presence_of_all_elements_located((By.XPATH, table_xpath))
                        )
                        row = rows[i]
                        cells = row.find_elements(By.TAG_NAME, "td")
                        cell = cells[cells.index(cell)]  # Возвращаемся к текущей ячейке
                        find_and_save_receipts(cell)
                    except Exception as e:
                        print(f"Error: {e}")

                print("-" * 20)  # Разделитель между строками

                # Ожидание загрузки страницы после возврата
                wait.until(EC.presence_of_all_elements_located((By.XPATH, table_xpath)))
                time.sleep(1)  # Небольшая задержка для стабильности

            break  # Останавливаем цикл, когда все строки обработаны

        except Exception as e:
            print(f"Ошибка при загрузке таблицы или обработке данных: {e}")
            time.sleep(1)
            if driver.current_url != "https://cabinet.tax.gov.ua/documents/in":
                driver.back()

            break



def go_to_page(page_number):
    try:
        while True:
            # Получаем текущий номер страницы
            current_page = driver.find_element(By.CSS_SELECTOR, ".p-paginator-page.p-highlight").text
            if int(current_page) == page_number:
                print(f"Reached page {page_number}")
                return True

            # Кликаем на кнопку перехода на следующую страницу
            next_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@class='p-paginator-next p-paginator-element p-link p-ripple']"))
            )
            next_button.click()
            time.sleep(0.5)  # Небольшая задержка для стабильности

    except Exception as e:
        print(f"Error: {e}")
        return False


# Функция для перехода на следующую страницу
def go_to_next_page(page_xpath):
    try:
        # Ожидание появления и кликабельности кнопки "Следующая страница"
        next_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, page_xpath))
        )
        if next_button.is_enabled():
            try:
                next_button.click()
            except ElementClickInterceptedException:
                print(
                    "ElementClickInterceptedException: Кнопка перекрыта другим элементом. Выполняем клик с помощью JavaScript."
                )
                driver.execute_script("arguments[0].click();", next_button)
            return True
    except NoSuchElementException:
        print(
            "NoSuchElementException: Кнопка перехода на следующую страницу не найдена."
        )
    except TimeoutException:
        print("TimeoutException: Кнопка перехода на следующую страницу не кликабельна.")
    except Exception as e:
        print(f"Error: {e}")

    return False


async def authorizations():
    try:
        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/login")

        # Ожидаем, пока блокирующий элемент исчезнет
        WebDriverWait(driver, 10).until(
            EC.invisibility_of_element_located((By.CLASS_NAME, "p-blockui-document"))
        )

        # Находим элемент dropdown и кликаем по нему, чтобы открыть список
        click_element_by_xpath("//p-dropdown[@id='selectedCAs111']")

        # Находим элемент с текстом 'КНЕДП ТОВ "Центр сертифікації ключів "Україна"' и кликаем по нему
        click_element_by_xpath(
            '//span[text()=\'КНЕДП ТОВ "Центр сертифікації ключів "Україна"\']'
        )

        el_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[2]/div/div/button/span"
        # Находим кнопку и кликаем по ней
        button = driver.find_element(By.XPATH, el_path)
        button.click()

        # Ожидаем появления окна выбора файла
        time.sleep(2)

        # Используем pyperclip для копирования пути к файлу в буфер обмена и pyautogui для вставки
        cur_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(cur_dir, "41098985_2634316155_DU240119101324.ZS2")

        # Копируем путь к файлу в буфер обмена
        pyperclip.copy(file_path)

        # Вставляем путь из буфера обмена
        pyautogui.hotkey("ctrl", "v")
        # pyautogui.write(file_path)  # некорректно вводит путь к файлу. заменяет символы на другие
        pyautogui.press("enter")

        # Находим поле для ввода пароля и вводим пароль
        psw_path = "/html/body/app-root/div/div[2]/div/app-login/div/div[2]/div/cb-sign-ui/div[1]/div/div/div[3]/div[4]/div/div/input"
        password_input = driver.find_element(By.XPATH, psw_path)
        password_input.send_keys("41098985")

        # Находим кнопку "Зчитати" и кликаем по ней
        read_button = driver.find_element(By.XPATH, "//span[text()='Зчитати']")
        read_button.click()

        # Ожидаем пока прочитается файл
        time.sleep(5)

        # Находим кнопку "Увійти" и кликаем по ней
        login_button = driver.find_element(By.XPATH, "//span[text()='Увійти']")
        login_button.click()
        time.sleep(1)

    except NoSuchElementException as e:
        print(f"страницы закончились: {e}")

    except Exception as e:
        print(e)


# загружает квитанции с сайта tax.gov.ua
async def main_taxgovua_receipt():
    try:
        # Авторизация
        await authorizations()

        # Открываем сайт
        driver.get("https://cabinet.tax.gov.ua/documents/in")

        time.sleep(1)

        # Находим кнопку "Пошук" и кликаем по ней
        click_element_by_xpath(
            "//span[@class='p-button-label ng-star-inserted' and text()='Витяги з реєстрів']"
        )

        # Ожидаем загрузки страницы с таблицей
        table_xpath = "//tbody[@class='p-datatable-tbody']/tr"
        page_xpath = "//button[@class='p-paginator-next p-paginator-element p-link p-ripple']"

        go_to_page(1)

        # Получаем данные с первой страницы
        get_table_data(table_xpath)

        # Проходим по всем страницам и собираем данные
        while True:
            if not go_to_next_page(page_xpath):
                break
            # time.sleep(5)  # Ожидаем загрузки новой страницы
            get_table_data(table_xpath)

        return True

    except Exception as e:
        print(e)

    return False


if __name__ == "__main__":
    asyncio.run(main_taxgovua_receipt())
