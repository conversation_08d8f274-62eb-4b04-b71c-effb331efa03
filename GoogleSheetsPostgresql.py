import gspread
import pandas as pd
from gspread_dataframe import set_with_dataframe
import os
import sys
from AsyncPostgresql import con_postgres_psycopg2
cur_dur = os.path.dirname(os.path.abspath(__file__))
sys.path.append(cur_dur)
# Авторизация с использованием service account
json_file = os.path.join(cur_dur, "prestige-429010-9481ce84f6f8.json")
sa = gspread.service_account(filename=json_file)

# Ensure the file_name matches exactly with your Google Sheets name
file_name = "Register"
sheet_name = "Medoc"


def google_sheets_update_register():
    try:
        # Open the spreadsheet
        sh = sa.open(file_name)
        wks = sh.worksheet(sheet_name)

        # Подключение к PostgreSQL
        conn = con_postgres_psycopg2()
        sql = '''
            SELECT
                doc.customer_edrpou ОКПО,
                doc.customer_name контрагент,
                doc.doc_number ДокНомер,
                doc.doc_date ДокДата,
                doc.amount сумма,
                doc.amount_vat НДС,
                concat(docinfo.docname,'; ',doc.payment_purpose ) назначение
            FROM t_medoc_sent_document as doc
                INNER JOIN v_medoc_reestr_and_docinfo as docinfo
                    ON docinfo.doc_id = doc.doc_id
            WHERE 
                doc.doc_date::date >= '01.08.2024'::date
            ORDER BY 
                doc.doc_date, 
                doc.doc_number
            ;
        '''
        df = pd.read_sql(sql, conn)
        if df.empty:
            print("No data available.")
            return

        df['сумма'] = df['сумма'].fillna(0)
        df['НДС'] = df['НДС'].fillna(0)

        df['сумма'] = df['сумма'].apply(lambda x: f"{x:.2f}".replace('.', ','))
        df['НДС'] = df['НДС'].apply(lambda x: f"{x:.2f}".replace('.', ','))

        # Преобразование всех колонок в текстовые
        df = df.astype(str)

        # Вставка DataFrame в Google Sheets
        set_with_dataframe(wks, df)

        conn.close()
    except gspread.exceptions.SpreadsheetNotFound:
        print(f"Spreadsheet '{file_name}' not found. Please check the name and permissions.")


if __name__ == "__main__":
    google_sheets_update_register()
