import base64
from OpenSSL import crypto


# 1. Читання файлу сертифікату
with open(r"c:\Rasim\Python\Medoc\ПРЕСТИЖ ПРОДУКТ.К (ВАЛЯЄВ Расім Сейранович) Підписання.crt" , "rb") as cert_file:
    cert_data = cert_file.read()

# 2. Конвертація сертифікату в BASE64
cert_base64 = base64.b64encode(cert_data).decode("utf-8")

# 3. ЄДРПОУ, який потрібно підписати
edrpou = "41098985"

# 4. Підписування за допомогою ЕЦП
with open(r"C:\Rasim\Python\Medoc\41098985_U240119100711.PCK", "rb") as key_file:
    private_key = crypto.load_privatekey(crypto.FILETYPE_PEM, key_file.read())

# Підписуємо ЄДРПОУ приватним ключем
signature = crypto.sign(private_key, edrpou.encode("utf-8"), "sha256")

# 5. Конвертація підпису в BASE64
signature_base64 = base64.b64encode(signature).decode("utf-8")

# 6. Готовий хедер для авторизації
authorization_header = f"{edrpou}.{signature_base64}.{cert_base64}"

print("Authorization header:", authorization_header)
