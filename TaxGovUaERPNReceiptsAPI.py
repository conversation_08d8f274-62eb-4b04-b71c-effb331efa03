# отбирает из бд блокированные НН/РК
# затем по этим док получает квитанции из API налоговой в формате PDF
# сохраняет в папку downloads/PDF
# и загружает в бд информацию о квитанциях

import asyncio
from datetime import datetime
import os
import time
from pathlib import Path
from dateutil.parser import parse
import pandas as pd
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

from AsyncPostgresql import async_save_pg
from ChangeKeyBoard import set_keyboard_layout
from Config import change_value_to_datetime
from ReadPDFByPytesseract import extract_receipt_date_and_number
from TaxGovUaConfig import authorizations, get_token

from AsyncPostgresql import engine
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_RECEIPT_BLOCK

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_driver_path = os.path.join(
    Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe"
)
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую

# Настройки Chrome
chrome_options = Options()

download_dir = os.path.join(cur_dir, "downloads", "pdf")
os.makedirs(download_dir, exist_ok=True)

# Убираем параметр headless для отображения графического интерфейса
# chrome_options.add_argument("--headless")

# Путь к драйверу Chrome
service = Service(chrome_driver_path)

# Запускаем браузер
driver = webdriver.Chrome(service=service, options=chrome_options)

# Авторизация на сайте
authorizations(driver)

driver, token = get_token(driver)


def get_receipts(code, impdate, reg_number, kvt_number, retries=3):
    global driver, token
    save_pdf_to_path = os.path.join(download_dir, f"{reg_number} {kvt_number}.pdf")
    url = f"https://cabinet.tax.gov.ua/ws/api/file/nlnkhd/pdf/kvt{kvt_number}?code={code}&impdate={impdate}"
    headers = {
        'Authorization': token,
        'Content-Type': 'application/pdf',
    }
    for attempt in range(retries):
        try:
            result = requests.get(url, headers=headers, timeout=10)  # Установка таймаута на 10 секунд
            if result.status_code == 200:
                with open(save_pdf_to_path, 'wb') as f:
                    f.write(result.content)
            else:
                print(f"Ошибка при получении данных из url: {result.text}\n{url}")
        except requests.exceptions.RequestException as e:
            print(f"Ошибка при запросе данных: {e}")
        except Exception as e:
            print(f"Неизвестная ошибка: {e}")
            # break  # Прерывание на случай непредвиденной ошибки

        driver, token = get_token(driver)  # Получаем новый токен
        headers['Authorization'] = token  # Обновляем заголовок с новым токеном

        time.sleep(5 * (1 + attempt))  # Ожидание перед повторной попыткой

    return save_pdf_to_path


# сохраняем из бд в df блокированные НН/РК
def get_blocks_docs_df():
    sql = """SELECT * FROM t_tax_cabinet_erpn_api_block WHERE COALESCE(filename,'') = ''"""
    df = pd.read_sql(sql, engine)
    return df


def save_pdf(row, kvt):
    code = row.get("code")
    impdate = row.get("reg_date")
    docrnn = row.get("reg_number")
    path_to_pdf = get_receipts(code, impdate, docrnn, kvt)
    return path_to_pdf


def extract_blocks_docs():
    while True:
        df = get_blocks_docs_df()
        if df.empty:
            print("Нет данных для обработки")
            break

        for index, row in df.iterrows():
            kvt = int(row.get('kvt_number'))

            # # *********
            # df["path_doc"] = df.apply(
            #     lambda row: os.path.join(
            #         download_dir, f"{row.get('code')} {int(row.get('kvt_number'))}.pdf"
            #     ),
            #     axis=1,
            # )
            #
            # # для НН/РК
            # df_doc = df[df["kvt_number"] == 999]
            # df_doc["urls_doc"] = df_doc.apply(
            #     lambda row: url + f"?code={row.get('code')}&impdate={row.get('reg_date')}",
            #     axis=1,
            # )
            # urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
            # responses = await run_map_get_list(session, urls_doc)
            #
            # # для квитанции 1
            # df_doc = df[df["kvt_number"] == 1]
            # df_doc["urls_doc"] = df_doc.apply(
            #     lambda row: url
            #                 + f"/kvt?code={row.get('code')}&impdate={row.get('reg_date')}",
            #     axis=1,
            # )
            # urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
            # responses = await run_map_get_list(session, urls_doc)
            #
            # # для квитанций 2, 3, 4
            # df_doc = df[df["kvt_number"].isin([2, 3, 4])]
            # df_doc["urls_doc"] = df_doc.apply(
            #     lambda row: url
            #                 + f"/kvt{int(row.get('kvt_number'))}?code={row.get('code')}&impdate={row.get('reg_date')}",
            #     axis=1,
            # )
            # urls_doc = dict(zip(df_doc["urls_doc"], df_doc["path_doc"]))
            # responses = await run_map_get_list(session, urls_doc)
            #
            # *********


            path_pdf = save_pdf(row, kvt)
            if path_pdf and os.path.exists(path_pdf):
                print(f"Создан файл: {path_pdf}")
                receipt_date, receipt_number = extract_receipt_date_and_number(path_pdf)
                if receipt_date:
                    save_receipt_data_to_db(row, receipt_date, receipt_number, kvt)
            elif kvt == 2:
                print(f"Не найден файл: {row.get('reg_number')} {kvt}")
                pass


def save_receipt_data_to_db(row, receipt_date, receipt_number, kvt_number):
    code = row.get("code")
    ijcode = row.get("ijcode")
    reg_date = change_value_to_datetime(row.get("reg_date"))
    receipt_date = change_value_to_datetime(receipt_date)
    reg_number = row.get("reg_number")
    receipt_status = row.get("receipt_status")
    data = (
        code,
        ijcode,
        reg_date,
        reg_number,
        receipt_date,
        receipt_number,
        receipt_status,
        kvt_number
    )
    result = asyncio.run(async_save_pg(SQL_INSERT_RECEIPT_BLOCK, [data]))
    if not result:
        print(f"{receipt_number} not saved to db")


if __name__ == '__main__':
    extract_blocks_docs()
