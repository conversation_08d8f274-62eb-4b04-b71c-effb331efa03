# проходит циклом по всем файлам в папке,
# считывает данные из файлов Excel и сохраняет их в базу данных PostgreSQL
# данные в файлах содержат информацию о НН и РК из ЕРПН сайта tax.gov.ua

import asyncio
import os

import pandas as pd
from sqlalchemy import create_engine

from AsyncPostgresql import async_save_pg
from Config import username, psw, basename, hostname_public, port

# Создание соединения с базой данных. Используется в pandas.to_sql
engine = create_engine(
    f"postgresql+psycopg2://{username}:{psw}@{hostname_public}:{port}/{basename}"
)

TABLE_NAME = "t_tax_cabinet_erpn"

# Функция для создания таблицы, если она не существует, и добавления комментариев
CREATE_TABLE_TAX_CABINET_ERPN = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME}(
        id serial PRIMARY KEY,
        doc_date date NOT NULL,
        doc_number numeric(13) NOT NULL,
        register_date timestamp(0) NOT NULL,
        customer_name varchar(200) NOT NULL,
        register_number numeric(13) NOT NULL,
        customer_edrpou varchar(20) NOT NULL,
        customer_inn varchar(20) NOT NULL,
        amount numeric(13, 2) NOT NULL,
        amount_vat numeric(13, 2) NOT NULL,
        doc_status varchar(100) NOT NULL,
        filename varchar(255) NOT NULL,
        craeted_at timestamp DEFAULT now(),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (register_date, register_number, doc_number, customer_edrpou, doc_status)
    );
    COMMENT ON TABLE {TABLE_NAME} IS 'НН, РК разгруженные из Excel файлов';
    COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
    COMMENT ON COLUMN {TABLE_NAME}.register_number IS 'Реєстраційний номер';
    COMMENT ON COLUMN {TABLE_NAME}.register_date IS 'Дата реєстрації ПН/РК в ЄРПН';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата складання ПН/РК';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Порядковий № ПН/РК';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'Сума ПДВ';
    COMMENT ON COLUMN {TABLE_NAME}.doc_status IS 'Статус документа';
    COMMENT ON COLUMN {TABLE_NAME}.customer_name IS 'Назва контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.customer_edrpou IS 'ЄДРПОУ контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.customer_inn IS 'ІПН контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Обсяг операцій';
    COMMENT ON COLUMN {TABLE_NAME}.filename IS 'Имя файла';
"""

SQL_INSERT_TAX_CABINET_ERPN = f"""
    INSERT INTO {TABLE_NAME} (
        register_number,
        doc_number,
        doc_date,
        register_date,
        customer_name,
        customer_inn,
        amount,
        amount_vat,
        doc_status,
        customer_edrpou,
        filename        
        )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    ON CONFLICT (register_date, register_number, doc_number, customer_edrpou, doc_status)
    DO NOTHING
"""


# выводим список xml файлов в папке
def get_files_by_ext(path, ext):
    xml_files = [f for f in os.listdir(path) if f.endswith(ext)]
    return xml_files


# Функция для переименования колонок и преобразования типов
def df_rename_columns(df):
    rename_dict = {
        "Реєстраційний номер": "register_number",
        "Порядковий № ПН/РК": "doc_number",
        "Дата складання ПН/РК": "doc_date",
        "Дата реєстрації ПН/РК в ЄРПН": "register_date",
        "Найменування Покупця": "customer_name",
        "ІПН Покупця": "customer_inn",
        "Обсяг операцій": "amount",
        "Сумв ПДВ": "amount_vat",
        "Статус ПН/РК": "doc_status",
        "Податковий номер Покупця": "customer_edrpou",
    }

    # Переименование колонок
    df.rename(columns=rename_dict, inplace=True)

    # Удаление ненужных колонок
    df = df.loc[:, list(rename_dict.values())]

    # Преобразование столбцов с датами в формат datetime
    df.loc[:, "doc_date"] = pd.to_datetime(df["doc_date"], format="%d.%m.%Y")
    df.loc[:, "register_date"] = pd.to_datetime(df["register_date"], format="%d.%m.%Y")

    # Преобразование столбцов в строковый формат, если необходимо
    df["customer_edrpou"] = df["customer_edrpou"].astype(str)
    df["customer_inn"] = df["customer_inn"].astype(str)

    return df


def add_filenaname_to_df(df, filename):
    df["filename"] = filename
    return df


def crete_folder_if_not_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


# Перемещение прочитанных файлов в папку
def move_files_to_folder(file_from, path_to_xls_archive):
    crete_folder_if_not_exists(path_to_xls_archive)
    extract_file = os.path.basename(file_from)
    file_exists = os.path.exists(os.path.join(path_to_xls_archive, extract_file))
    if file_exists:
        os.remove(file_from)
    else:
        os.rename(file_from, os.path.join(path_to_xls_archive, extract_file))


# async def main_read_xls(path_to_xls_folder):
def main_read_xls(path_to_xls_folder):
    files = get_files_by_ext(path_to_xls_folder, ".xls")
    for file in files:
        print(file)
        df = pd.read_excel(os.path.join(path_to_xls_folder, file))
        # if df.empty:
        #     continue
        # Переименование колонок и преобразование типов
        df = df_rename_columns(df)
        df = add_filenaname_to_df(df, file)

        # Сохранение DataFrame в таблицу PostgreSQL
        # df.to_sql(TABLE_NAME, engine, if_exists="append", index=False)
        data_value = df.values.tolist()
        result = asyncio.run(async_save_pg(SQL_INSERT_TAX_CABINET_ERPN, data_value))
        # result = await async_save_pg(SQL_INSERT_TAX_CABINET_ERPN, data_value)
        if result:
            move_files_to_folder(os.path.join(path_to_xls_folder, file), os.path.join(path_to_xls_folder, "archive"))
        print(f"{result}, {TABLE_NAME}.")


if __name__ == "__main__":
    # Создание таблицы, если она не существует
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(async_save_pg(CREATE_TABLE_TAX_CABINET_ERPN))
    print(f"{result}, table {TABLE_NAME} created")

    cur_dir = os.path.dirname(__file__)
    folder_path = os.path.join(cur_dir, "downloads", "excel")
    main_read_xls(folder_path)
    print("Завершено")
