# https://cabinet.tax.gov.ua/documents/in
# сохраняются данные таблицы (Решения, Уведомления, Квитанции) в бд. Не сами файлы, а данные о них.

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_document_in"
SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        page_number integer NOT NULL,
        doc_date timestamp(0) NOT NULL,
        doc_type varchar(200) NOT NULL,
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
);
  
    COMMENT ON TABLE {TABLE_NAME} IS 'Решения, Уведомления, Квитанции';
    COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
    COMMENT ON COLUMN {TABLE_NAME}.page_number IS 'Номер страницы';
    COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата документа';
"""

SQL_INSERT_DOCUMENT_IN = f"""
  INSERT INTO {TABLE_NAME} (page_number, doc_date, doc_type)
  VALUES ($1, to_timestamp($2,'dd.mm.yyyy hh24:mi:ss'), $3)
  ;
"""


async def main_tax_cabinet():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_tax_cabinet())
