import psycopg2

from AsyncPostgresql import con_postgres_psycopg2


def delete_double_rows_from_table(table_name = 'medoc_reestr'):
    # Параметры подключения к базе данных
    conn = con_postgres_psycopg2()

    # Имя таблицы
    # table_name = "medoc_reestr"

    # SQL-запрос для получения наименований всех полей таблицы
    query_columns = f"""
    SELECT column_name
    FROM information_schema.columns
    WHERE table_name = '{table_name}'
    """

    # Выполнение запроса и получение результатов
    with conn.cursor() as cursor:
        cursor.execute(query_columns)
        columns = cursor.fetchall()

    # Формирование списка имен столбцов
    column_names = [column[0] for column in columns]

    # Формирование SQL-запроса для удаления дубликатов
    query_delete_duplicates = f"""
    DELETE FROM {table_name}
    WHERE ctid NOT IN (
        SELECT MIN(ctid)
        FROM {table_name}
        GROUP BY {', '.join(column_names)}
    )
    """

    # Выполнение запроса на удаление дубликатов
    with conn.cursor() as cursor:
        cursor.execute(query_delete_duplicates)
        conn.commit()

    # Закрытие соединения
    conn.close()


if __name__ == '__main__':
    delete_double_rows_from_table()