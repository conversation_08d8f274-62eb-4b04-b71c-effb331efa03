import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_all"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4 (),
        doc_date date NOT NULL, -- Дата документа
        doc_number varchar(20) NOT NULL, -- Номер документа
        register_date date NOT NULL, -- Дата реєстрації
        customer_name varchar(200) NOT NULL, -- Назва контрагента
        register_number numeric(13) NOT NULL, -- Реєстраційний номер
        INN_customer varchar(12) NOT NULL, -- І<PERSON><PERSON> контрагента
        amount numeric(13, 2) NOT NULL, -- Обсяг операцій
        amount_vat numeric(13, 2) NOT NULL, -- Сума ПДВ  
        doc_status varchar(100) NOT NULL, -- Статус документа
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (register_number, doc_number, doc_status)      
    );
  
  COMMENT ON TABLE {TABLE_NAME} IS 'НН, РК все';
  COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
  COMMENT ON COLUMN {TABLE_NAME}.register_number IS 'Реєстраційний номер';
  COMMENT ON COLUMN {TABLE_NAME}.register_date IS 'Дата реєстрації ПН/РК в ЄРПН';
  COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата складання ПН/РК';
  COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Порядковий № ПН/РК';
  COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'Сума ПДВ';
  COMMENT ON COLUMN {TABLE_NAME}.doc_status IS 'Статус документа';
  COMMENT ON COLUMN {TABLE_NAME}.customer_name IS 'Назва контрагента';
  COMMENT ON COLUMN {TABLE_NAME}.INN_customer IS 'ІПН контрагента';
  COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Обсяг операцій';  
"""

SQL_INSERT_TO_T_TAX_CABINET_ALL = f"""
  INSERT INTO {TABLE_NAME} (
      doc_date, doc_number, register_date, customer_name, register_number, INN_customer, amount, amount_vat, doc_status)
  VALUES (
    to_date($1,'dd.mm.yyyy'), $2, to_date($3,'dd.mm.yyyy'), $4, $5, $6, REPLACE(REPLACE($7,' ',''),',','.')::NUMERIC(13,2), 
    REPLACE(REPLACE($8,' ',''),',','.')::NUMERIC(13,2), $9)
--  ON CONFLICT (register_number, doc_number, doc_status)
--  DO UPDATE SET
--      doc_date = EXCLUDED.doc_date,
--      register_date = EXCLUDED.register_date,
--      customer_name = EXCLUDED.customer_name,
--      INN_customer = EXCLUDED.INN_customer,
--      amount = EXCLUDED.amount,
--      amount_vat = EXCLUDED.amount_vat,
--      doc_status = EXCLUDED.doc_status
"""


SQL_DELETE_DOUBLE = f"""
    
    DELETE FROM t_tax_cabinet_all
    WHERE id IN (
        SELECT id
        FROM (
            SELECT id, 
                ROW_NUMBER() OVER (PARTITION BY doc_date, doc_number, register_date, customer_name, register_number, inn_customer, amount, amount_vat, doc_status 
                                    ORDER BY id) AS row_num
            FROM t_tax_cabinet_all
        ) AS t
        WHERE t.row_num > 1
    );
    
"""

async def main_tax_cabinet_all():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == "__main__":
    asyncio.run(main_tax_cabinet_all())
