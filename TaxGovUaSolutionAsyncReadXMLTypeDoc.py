import asyncio
import os
from pathlib import Path
import re

import pandas as pd
from sqlalchemy import inspect, MetaData, Table, text
from sqlalchemy.exc import SQLAlchemyError

from AsyncPostgresql import async_save_pg, engine, get_df
from ReadXML2 import get_files_by_ext

current_path = os.path.dirname(os.path.abspath(__file__))
download_path = os.path.join(current_path, "downloads", "pdf", "solution")
os.makedirs(download_path, exist_ok=True)

SQL = """
    SELECT 
        id,
        cdoc,
        lower(concat(id,'.xml')) filename
    FROM t_tax_cabinet_solutions
    ORDER BY operdate DESC 
    ;
"""


async def move_pdf_to_another_folder(old_file):
    archive_dir = os.path.join(download_path, "archive")
    os.makedirs(archive_dir, exist_ok=True)
    new_file = os.path.join(archive_dir, os.path.basename(old_file))

    # Удаление нового файла, если он уже существует
    if os.path.exists(new_file):
        os.remove(new_file)

    os.rename(old_file, new_file)
    return new_file


async def get_filenames_from_db():
    df = await get_df(SQL)
    if not df.empty:
        df["path"] = download_path + "\\" + df["filename"]

    return df


async def read_xml_and_save_to_df(file_path):
    try:
        df = pd.read_xml(file_path, parser="lxml", encoding="utf-8")
    except UnicodeDecodeError:
        df = pd.read_xml(file_path, parser="lxml", encoding="windows-1251")
    except Exception as e:
        print(f"Ошибка при чтении файла {file_path}: {e}")
        df = pd.DataFrame()  # Возвращаем пустой DataFrame в случае ошибки

    # Применение функции для заполнения NaN значений в числовых столбцах
    df = df.apply(lambda x: x.fillna(0) if x.dtype.kind in "biufc" else x)
    df.columns = [col.lower() for col in df.columns]
    # Преобразование столбцов в int, затем в str и в datetime
    to_date = [
        "d_fill",
        "hdate",
        "hdate1",
        "hdate2",
        "r01g1d",
        "r01g2d",
        "r02g1d",
        "r02g4d",
        "r03g4d",
        "r03g5d",
    ]
    for col in to_date:
        if col in df.columns:
            df[col] = df[col].astype(int).astype(str)
            
            # Добавление ведущего нуля для даты, если количество символов равно 7
            df[col] = df[col].apply(lambda x: '0' + x if len(x) == 7 else x)
            df[col] = pd.to_datetime(df[col], format="%d%m%Y", errors="coerce")

    return df


# async def save_df_to_db(cdoc, df):
#     table_name = f"t_tax_cabinet_{cdoc.lower()}"
#     try:
#         df.to_sql(table_name, engine, if_exists="append", index=False, method="multi")
#         # print(f"Данные успешно сохранены в таблицу {table_name}.")
#         return True
#     except SQLAlchemyError as e:
#         print(f"Ошибка при сохранении данных в таблицу {table_name}: {e}")

#     return False


async def save_df_to_db(cdoc, df):
    table_name = f"t_tax_cabinet_{cdoc.lower()}"

    # Получение списка колонок из таблицы
    inspector = inspect(engine)
    columns_in_table = [col["name"] for col in inspector.get_columns(table_name)]

    # Оставляем в DataFrame только те колонки, которые есть в таблице
    df = df[[col for col in df.columns if col in columns_in_table]]
    df = await delete_from_df(df)
    try:
        df.to_sql(table_name, engine, if_exists="append", index=False, method="multi")
        # print(f"Данные успешно сохранены в таблицу {table_name}.")
        return True
    except SQLAlchemyError as e:
        print(f"Ошибка при сохранении данных в таблицу {table_name}: {e}")

    return False


async def delete_from_df(df):
    return df.dropna(subset=["hnum"])

async def main():
    cur_dir = os.path.dirname(__file__)
    folder_path = os.path.join(cur_dir, "downloads", "pdf", "solution")
    files = get_files_by_ext(folder_path, ".xml")
    df_db = await get_filenames_from_db()
    for index, row in df_db.iterrows():
        file = row["filename"]
        if file not in files:
            continue

        xml_file_path = row.get("path")
        cdoc = row.get("cdoc")
        # Чтение XML файла
        df = await read_xml_and_save_to_df(xml_file_path)
        if not df.empty:
            df["filename"] = file
            result = await save_df_to_db(cdoc, df)
            if result:
                new_file_path = await move_pdf_to_another_folder(xml_file_path)
                print(f"Файл {xml_file_path} успешно обработан.")
            else:
                print(f"Файл {xml_file_path} не был обработан.")


if __name__ == "__main__":
    asyncio.run(main())
