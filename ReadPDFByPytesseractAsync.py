"""
OCR Engine Mode (OEM) Options:
--oem 0: Только старый движок.
--oem 1: Только LSTM (нейронные сети).
--oem 2: Старый + LSTM движки.
--oem 3: По умолчанию, в зависимости от доступности.

Page Segmentation Mode (PSM) Options:
--psm 0: Только определение ориентации и скрипта (OSD).
--psm 1: Автоматическая сегментация страницы с OSD.
--psm 2: Автоматическая сегментация страницы, но без OSD или OCR.
--psm 3: Полностью автоматическая сегментация страницы, но без OSD.
--psm 4: Предполагается один столбец текста переменного размера.
--psm 5: Предполагается один однородный блок вертикально выровненного текста.
--psm 6: Предполагается один однородный блок текста.
--psm 7: Обработка изображения как одной строки текста.
--psm 8: Обработка изображения как одного слова.
--psm 9: Обработка изображения как одного слова в круге.
--psm 10: Обработка изображения как одного символа.
--psm 11: Разреженный текст. Найти как можно больше текста в произвольном порядке.
--psm 12: Разреженный текст с OSD.
--psm 13: Сырой текст. Обработка изображения как одной строки текста, обходя специфические для Tesseract хаки.
"""

import asyncio
import os
import re
from pathlib import Path
import cv2
import numpy as np
import pytesseract
from pdf2image import convert_from_path

cur_dir = os.path.dirname(os.path.abspath(__file__))
# установить путь в PATH
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
poppler_path = os.path.join(cur_dir, "poppler-24.07.0", "Library", "bin")


# после чтения pdf-файла, необходимо удалить все файлы из папки, где хранятся временные изображения
async def remover_all_files_in_folder(extension):
    directory = Path(poppler_path)
    try:
        # Удаляем все файлы с расширением .ppm
        [ppm_file.unlink() for ppm_file in directory.rglob("*.ppm")]
    except Exception as e:
        pass


def preprocess_image(image):
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]
    gray = cv2.medianBlur(gray, 3)
    return gray


async def extract_text_from_pdf(pdf_path):
    # loop = asyncio.get_event_loop()
    # pages = await loop.run_in_executor(None, convert_from_path, pdf_path, 700, poppler_path)
    loop = asyncio.get_event_loop()
    text = ""
    try:
        pages = await loop.run_in_executor(None, convert_from_path, pdf_path, 300, poppler_path)
        # print(f"Количество страниц: {pdf_path} {len(pages)}")
        for page in pages:
            open_cv_image = cv2.cvtColor(np.array(page), cv2.COLOR_RGB2BGR)
            processed_image = preprocess_image(open_cv_image)
            custom_config = r"--oem 1 --psm 4"
            text += await loop.run_in_executor(
                None, pytesseract.image_to_string, processed_image, "ukr+eng", custom_config
            )
    except Exception as e:
        print(f"Ошибка при обработке файла {pdf_path}: {e}")

    # Удаление символов []{}|#
    text = re.sub(r"[{}|#\]\[]+", " ", text)  

    # Замена последовательностей из двух и более пробелов на один пробел
    text = re.sub(r"\s{2,}", " ", text)  

    return text


def get_receipt_date(text):
    pattern = "\d{2}.\d{2}.\d{4} \d{2}:\d{2}:\d{2}"
    date = re.findall(pattern, text)
    if date:
        return date[0]
    return None


def get_receipt_date_short(text):
    pattern = "\d{2}.\d{2}.\d{4}"
    date = re.findall(pattern, text)
    if date:
        return date[0]
    return None


def get_number_receipt(raw_date, text):
    if not raw_date:
        return None
    raw_date = raw_date.split()[0]
    pattern = f"{raw_date}.*?(\d+/\d+)|(\d+/\d+/*\d*)"
    raw_number = re.findall(pattern, text)
    if raw_number:
        for nm in raw_number:
            if nm[0]:
                return nm[0]
            if nm[1]:
                return nm[1]
    return None


def get_number_by_date_short(raw_date, text):
    pattern = f"{raw_date}.*?(\d+/\d+)|(\d+/\d+/*\d*)"
    raw_number = re.findall(pattern, text)
    if raw_number:
        for nm in raw_number:
            if nm[0]:
                return nm[0]
            if nm[1]:
                return nm[1]
    return None


async def extract_receipt_date_and_number_async(pdf_path):
    extracted_text = await extract_text_from_pdf(pdf_path)
    date_receipt = get_receipt_date(extracted_text)
    number_receipt = get_number_receipt(date_receipt, extracted_text)
    print(date_receipt, number_receipt)
    return date_receipt, number_receipt


async def extract_date_short_and_number_async(pdf_path):
    extracted_text = await extract_text_from_pdf(pdf_path)
    date_receipt = get_receipt_date_short(extracted_text)
    if not date_receipt:
        return date_receipt, None
    number_receipt = get_number_by_date_short(date_receipt, extracted_text)
    print(date_receipt, number_receipt)
    return date_receipt, number_receipt


async def get_number(text):
    pattern = "\d{7,10}/41098985"
    result = re.findall(pattern, text)
    if not result:
        result = re.search("(\d+/)", text)
        if result:
            result = result.group() + "41098985"
    else:
        result = result[0]
    return result if result else None


async def get_date(text):
    pattern = "\d{2}.\d{2}.\d{4}"
    result = re.findall(pattern, text)
    return result[0] if result else None


# Функция для проверки, содержит ли строка только символы
def is_only_symbols(s):
    return bool(re.match(r"^[^\w\d]+$", s))


# определение типа документа, Решение по таблице или Решение по НН,РК
async def get_doc_type(text):
    doc_type = re.search("Додаток.?\d*", text)
    if doc_type:
        doc_type = doc_type.group().lower()
        if doc_type in ["додаток 6", "додаток 7"]:
            doc_type = f"{doc_type} таблица"
    return doc_type


async def extract_other_data(pdf_path):
    # подходит под док "Решение", по НН,РК
    # ожидаемый результат:
    # ['06.10.2022', '10334', 'ПН', '51468.48', '8578.08', '29.11.2022', '7733165/41098985', '265750110300425']
    try:
        if not os.path.exists(pdf_path):
            # если файл обработан другим процессом, просто пропускаем его
            return None
        text = await extract_text_from_pdf(pdf_path)
        doc_type = await get_doc_type(text)
        if "таблица" in doc_type:
            return doc_type

        id = Path(pdf_path).stem.split()[0]
        text = text.replace(",", ".")
        doc_date = await get_date(text)
        doc_number = await get_number(text)
        text = text.replace("податкова накладна", "ПН")

        # Удаление пробела после знака минус, если за ним следует число
        text = re.sub(r"-\s+(\d)", r"-\1", text)

        # Удаление " 1 " после даты, если за ним следует число
        text = re.sub(r"(\d{2}\.\d{2}\.\d{4})\s+1\s+(\d+)", r"\1 \2", text)

        extracted_text = re.search("\d{2}\.\d{2}\.\d{4}\s+\d+.*", text)
        if extracted_text and doc_date and doc_number:
            extracted_text = extracted_text.group().split()

            # Удаление элементов, которые содержат только символы
            extracted_text = [
                item for item in extracted_text if not is_only_symbols(item)
            ]

            extracted_text = extracted_text[:5]
            # del extracted_text[2:4]
            extracted_text.append(doc_date)
            extracted_text.append(doc_number)
            extracted_text.append(id)
            return extracted_text
    except Exception as e:
        print(f"Ошибка при обработке файла {pdf_path}: {e}")
        return None


if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))
    pdf_path = os.path.join(
        current_dir, "downloads", "pdf", "solution", "265750038913106.pdf"
    )
    asyncio.run(extract_other_data(pdf_path))

    # C:\Rasim\Python\Medoc\downloads\pdf\solution\265750110300425.pdf ПН
    # ['06.10.2022', '10334', 'Пн', '51468.48', '8578.08']
