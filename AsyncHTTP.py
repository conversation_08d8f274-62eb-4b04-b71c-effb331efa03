import asyncio
import json
import time
import os
from datetime import datetime
from itertools import islice
from dotenv import load_dotenv
import aiohttp
from dateutil.relativedelta import relativedelta


load_dotenv()
hostname_public = os.getenv('PG_HOST')

async def fetch_url(session, url, count=1):
    try:
        async with session.get(url) as response:
            if response.status == 200:
                result = await response.text()
                if result:  # Проверка на пустой ответ
                    return json.loads(result)
                else:
                    print(f"Пустой ответ от сервера: {url}")
                    return None
            elif response.status == 500 and count < 5:
                time.sleep(5 * count)
                count += 1
                await fetch_url(session, url)
            else:
                print(f"Ошибка при получении данных из url: {response.status}: {url}")
    except Exception as e:
        print(f"Возникла ошибка при получении данных из url: {url}")
        print(e)


async def fetch_one_url(url):
    async with aiohttp.ClientSession() as session:
        response = await fetch_url(session, url)
        return response


# async def fetch_all_urls(urls):
#     async with aiohttp.ClientSession() as session:
#         tasks = [fetch_url(session, url) for url in urls]
#         result = await asyncio.gather(*tasks)
#         return result


async def fetch_all_urls(urls):
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url(session, url) for url in urls]
        results = []

        while tasks:
            done, tasks = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            results.extend([task.result() for task in done])

            # Отправляем следующую пачку запросов
            for future in islice(tasks, 5):
                done, _ = await asyncio.wait([future])
                results.extend([task.result() for task in done])

        return results


if __name__ == '__main__':
    date_last = datetime.today().date().strftime("%Y/%m/%d")
    date_first = (datetime.today().date() - relativedelta(months=3)).strftime("%Y/%m/%d")
    url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
           f"&docType=-1&moveType=0&dateFrom={date_first}&dateEnd={date_last}")
    asyncio.run(fetch_one_url(url))
