import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_bad_files"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4 (),
        bad_file_name varchar(200) NOT NULL, -- Название файла
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (bad_file_name)      
    );
  
  COMMENT ON TABLE {TABLE_NAME} IS 'файлы которые не удалось обработать';
  COMMENT ON COLUMN {TABLE_NAME}.id IS 'Ідентифікатор';
  COMMENT ON COLUMN {TABLE_NAME}.bad_file_name IS 'Название файла';
"""

SQL_INSERT_BAD_FILE_NAME = f"""
  INSERT INTO {TABLE_NAME} (bad_file_name) VALUES ($1)
  ON CONFLICT (bad_file_name) DO NOTHING;
"""


async def main_tax_cabinet_bad_files():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == "__main__":
    asyncio.run(main_tax_cabinet_bad_files())
