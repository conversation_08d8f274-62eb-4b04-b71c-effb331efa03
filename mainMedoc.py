# http://*************:63777/Swagger/index.html

# pip install python-dateutil
import asyncio
from datetime import date, datetime
from dateutil.parser import parse
import xmltodict
from dateutil.relativedelta import relativedelta
from dateutil import parser

from AsyncHTTP import fetch_one_url
from DeleteDoubleRowsFromTable import delete_double_rows_from_table
from GoogleSheetsPostgresql import google_sheets_update_register
from MedocConfig import doc_type_name
from SaveDocToPdf import create_table, main_medoc_save_doc_to_pdf
from Views.vwReestrAndDocInfo import vw_reestr_and_docinfo_async
from tGetDocInfo import save_doc_info
from tRegister import save_register
from AsyncPostgresql import hostname_public, clear_table

# pd.set_option('display.max_columns', None)

doc_type = doc_type_name['Всі']  # 10100


# Отримання квитанцій документа
async def get_url_checks(cardcode):
    return f"http://{hostname_public}:63777/api/Info/GetDocKVT?cardCode={cardcode}"


# Пошук і отримання ідентифікатора установи за заданими ЄДРПОУ установи та номером філії
async def get_idorg(my_okpo) -> int:
    url = f"http://{hostname_public}:63777/api/System/GetOrgCode?edrpou={my_okpo}"
    response = await fetch_one_url(url)
    return response


async def get_counterparty_name(xml_data):
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HNAMEBUY']
    finally:
        return result


def get_doc_status(xml_data):
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HRESULT']
    finally:
        return result


async def get_url(**kwargs):
    idorg = kwargs['idorg']
    datefrom = kwargs['datefrom']
    dateend = kwargs['dateend']
    return (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg={idorg}"
            f"&docType={doc_type}&moveType=0&dateFrom={datefrom}&dateEnd={dateend}")


def get_date_from(control_date):
    if isinstance(control_date, datetime):
        control_date = control_date.date()
    elif isinstance(control_date, date):
        control_date = date_first
    else:
        control_date = parser.parse(control_date).date()
    return control_date


async def main_medoc(date_first: datetime.date):
    await create_table()
    # await clear_table('medoc_reestr')
    # await clear_table('medoc_doc_info')
    date_first = get_date_from(date_first)
    # текущая дата
    while date_first <= datetime.today().date():
        # добавляем месяц к текущей дате
        date_last = date_first + relativedelta(weeks=1)
        date_one = date_first.strftime("%Y/%m/%d")
        date_two = date_last.strftime("%Y/%m/%d")
        print(date_one, date_two)
        url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
               f"&docType={doc_type}&moveType=0&dateFrom={date_one}&dateEnd={date_two}")
        df = await save_register(url)
        if not df.empty:  # может быть None, если нет данных в периоде
            await save_doc_info(df)            
        date_first = date_last + relativedelta(days=1)

    await vw_reestr_and_docinfo_async()
    delete_double_rows_from_table('medoc_reestr')
    delete_double_rows_from_table('medoc_doc_info')

    # отправляем менеджерам на почту счета в PDF
    await main_medoc_save_doc_to_pdf()

    # обновляем таблицу в Google Sheets (закладка Medoc)
    google_sheets_update_register()


if __name__ == '__main__':
    start = datetime.now()
    print(start)
    date_first = datetime.today().date() - relativedelta(months=1)
    asyncio.run(main_medoc(date_first))
    print(datetime.now(), datetime.now() - start)

