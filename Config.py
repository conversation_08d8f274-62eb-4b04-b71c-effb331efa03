import os
import sys
from datetime import datetime
from pathlib import Path
from dateutil.parser import parse
import pandas as pd

my_path = os.path.dirname(os.path.abspath(__file__))
path = Path(my_path)
components = path.parts
python_index = None
if "Python" in components:
    python_index = components.index("Python")
my_path_list = str(my_path).split('\\')[1:python_index + 1]
config_path = os.path.join(os.path.splitdrive(my_path)[0], '\\', *my_path_list, "Config")
sys.path.append(my_path)
sys.path.append(config_path)
sys.path.append(path.parent.__str__())

from configPrestige import username, psw, basename, hostname_public, port, hostname_public, CONN_PARAMS


def update_libs():
    import subprocess
    import sys
    # Get a list of all installed packages
    result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--outdated', '--format=freeze'], capture_output=True,
                            text=True)
    packages = [line.split('==')[0] for line in result.stdout.splitlines()]

    # Update each package
    for package in packages:
        subprocess.call([sys.executable, '-m', 'pip', 'install', '--upgrade', package])


def change_value_to_datetime(value):
    if isinstance(value, datetime):
        return value
    elif isinstance(value, str):
        return parse(value)
    elif isinstance(value, pd.Timestamp):
        return value.to_pydatetime()
    elif isinstance(value, int):
        return datetime.fromtimestamp(value)
    else:
        return None


if __name__ == '__main__':
    update_libs()
