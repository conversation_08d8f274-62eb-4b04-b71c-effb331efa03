# -*- coding: utf-8 -*-

"""
downloadPdfGUI.py
https://aistudio.google.com/prompts/1T6A_xzSS7Mol8SihX7R716K_s7NcrGO7

to exe
pyinstaller --onefile --windowed --name="MedocDownloader" downloadPdfGUI.py

================================================================================
НАЗНАЧЕНИЕ ФАЙЛА:
--------------------------------------------------------------------------------
Этот скрипт предназначен для асинхронной загрузки первичных документов
(например, счетов, актов) в формате PDF из API системы электронного
документооборота "M.E.Doc".

Скрипт находит все документы для указанного контрагента (по коду ЕГРПОУ)
за заданный период времени и сохраняет их в локальную папку.

Файлы раскладываются по подпапкам в зависимости от типа документа и периода.

================================================================================
КЛЮЧЕВЫЕ ОСОБЕННОСТИ:
--------------------------------------------------------------------------------
1.  АСИНХРОННОСТЬ:
    Использует библиотеки `asyncio` и `aiohttp` для выполнения множества
    сетевых запросов параллельно, что значительно ускоряет процесс загрузки
    большого количества документов.

2.  ОГРАНИЧЕНИЕ НАГРУЗКИ (SEMAPHORE):
    Чтобы не перегружать API-сервер и избегать ошибок, количество
    одновременных запросов на скачивание PDF ограничено (по умолчанию 5).

3.  РАЗБИВКА БОЛЬШИХ ДИАПАЗОНОВ ДАТ:
    Если запрашиваемый период превышает один месяц, скрипт автоматически
    разбивает его на месячные интервалы. Это предотвращает таймауты и ошибки
    на стороне API при запросе слишком большого объема данных за раз.

4.  НАДЕЖНОСТЬ И ОБРАБОТКА ОШИБОК:
    - Корректно обрабатывает ошибки сети и таймауты.
    - Решает специфическую проблему `TransferEncodingError`, принудительно
      закрывая соединение после каждого запроса (`Connection: close`).
    - Ведет подробное логирование всех шагов и возможных проблем.

5.  КОНФИГУРАЦИЯ ЧЕРЕЗ .ENV:
    Сетевые настройки (адрес сервера) вынесены в переменные окружения,
    которые можно задать в файле `.env`.

================================================================================
ЗАВИСИМОСТИ:
--------------------------------------------------------------------------------
- aiohttp
- python-dotenv
- python-dateutil
- collections (встроенная)

Установка: pip install aiohttp python-dotenv python-dateutil

================================================================================
КАК ИСПОЛЬЗОВАТЬ:
--------------------------------------------------------------------------------
1.  Создайте файл `.env` в той же директории, что и скрипт.
2.  Добавьте в него переменную `PG_HOST_LOCAL=ВАШ_IP_АДРЕС_СЕРВЕРА`.
3.  Запустите скрипт: python ваш_скрипт.py
4.  В открывшемся окне введите данные и нажмите "Скачать документы".
5.  Загруженные файлы появятся в папке, названной кодом партнера,
    рассортированные по подпапкам (`Продажа`, `Акт` и т.д.).
================================================================================
"""

import asyncio
import base64
import os
import re
import logging
import sys
import queue
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict
from dotenv import load_dotenv
import aiohttp
from dateutil.relativedelta import relativedelta
from dateutil.parser import parse

# ==============================================================================
# --- БЛОК ВАШЕГО РАБОЧЕГО СКРИПТА (BACKEND) ---
# ==============================================================================

load_dotenv()

logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- Конфигурация ---
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
ID_ORG = 781


# --- ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ---

def clean_filename(filename: str) -> str:
    """Удаляет недопустимые символы из имени файла и лишние пробелы."""
    return re.sub(r'[\\/*?:"<>|]', "", filename).strip()


def doc_type_short(doc_type: str) -> str:
    doc_type = doc_type.lower()
    if doc_type == 'податкова накладна':
        return 'ПН'
    else:
        return doc_type.upper()


def get_doc_type_name(docname: Optional[str]) -> str:
    """
    Извлекает из имени документа описательную часть до знака '№'.
    Если знак '№' в строке отсутствует, возвращает исходную строку.
    Возвращает '_Інше' для пустого ввода.
    """
    if not docname:
        return "_Інше"
    separator_index = docname.find('№')
    if separator_index == -1:
        return doc_type_short(docname.strip())
    return doc_type_short(docname[:separator_index].strip())


def split_date_range_by_month(start_date_str: str, end_date_str: str) -> List[Tuple[date, date]]:
    date_format = '%Y/%m/%d'
    start_dt = datetime.strptime(start_date_str, date_format).date()
    end_dt = datetime.strptime(end_date_str, date_format).date()
    if start_dt + relativedelta(months=1) > end_dt:
        return [(start_dt, end_dt)]
    print("Диапазон дат слишком большой. Разбиваю на месячные интервалы...")
    date_ranges = []
    current_start = start_dt
    while current_start <= end_dt:
        chunk_end = current_start + relativedelta(months=1) - relativedelta(days=1)
        if chunk_end > end_dt:
            chunk_end = end_dt
        date_ranges.append((current_start, chunk_end))
        current_start = chunk_end + relativedelta(days=1)
    return date_ranges


# --- ОСНОВНЫЕ АСИНХРОННЫЕ ФУНКЦИИ ---

async def fetch_one_url(session: aiohttp.ClientSession, url: str, semaphore: asyncio.Semaphore) -> Optional[Any]:
    async with semaphore:
        try:
            headers = {"Connection": "close"}
            timeout = aiohttp.ClientTimeout(total=60)
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Ошибка запроса к {url}. Статус: {response.status}, Ответ: {await response.text()}")
                    return None
        except Exception as e:
            logging.error(f"Непредвиденная ошибка при запросе к {url}: {e}")
            return None


async def get_document_as_pdf(session: aiohttp.ClientSession, doc: Dict[str, Any], semaphore: asyncio.Semaphore,
                              facsimile: bool, suffix: str = "") -> Optional[str]:
    doc_id = doc.get('doc_id')
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/PrintDocPDF?idOrg={ID_ORG}&docID={doc_id}&facsimile={str(facsimile).lower()}"
    data = await fetch_one_url(session, url, semaphore)
    if not data:
        logging.warning(f"Нет ответа от API для doc_id: {doc_id} (facsimile={facsimile}).")
        return None
    try:
        if not data or not isinstance(data, list) or not isinstance(data[0], dict):
            logging.error(f"Неожиданный формат ответа API для doc_id: {doc_id}")
            return None

        document_info = data[0]
        file_raw = document_info.get('File')
        file_name_from_api = document_info.get('FileName')
        doc_type = get_doc_type_name(file_name_from_api)
        output_dir = clean_filename(doc['partner_edrpou'])

        if not file_raw:
            logging.error(f"В ответе API отсутствует 'File' для doc_id: {doc_id}")
            return None

        base_name = ""
        period = ''
        if file_name_from_api:
            doc_num = doc.get('doc_num', '')
            doc_date_str = doc.get('doc_date')
            formatted_date = ''
            if doc_date_str:
                try:
                    parsed_date = parse(doc_date_str, dayfirst=True)
                    formatted_date = parsed_date.strftime('%d %m %Y')
                    period = parsed_date.strftime('%Y%m')
                except (ValueError, TypeError):
                    pass
                base_name = f"{doc_type} {doc_num} {formatted_date}"

        if not base_name:
            logging.error(f"Не удалось сформировать базовое имя для doc_id: {doc_id}. Используется запасное имя.")
            base_name = f"UNNAMED_{doc_id}"

        final_name = base_name.replace('.', ' ').upper()
        final_file_name = f"{clean_filename(final_name)}{suffix}.pdf"
        save_to = os.path.join(output_dir, period, doc_type)
        os.makedirs(save_to, exist_ok=True)
        file_path = os.path.join(save_to, final_file_name)

        file_data = base64.b64decode(file_raw)
        with open(file_path, 'wb') as f:
            f.write(file_data)
        return file_path
    except Exception as e:
        logging.error(f"Произошла непредвиденная ошибка при обработке doc_id {doc_id}: {e}")
        return None


async def download_documents_for_partner(session: aiohttp.ClientSession, partner_edrpou: str, date_from: str,
                                         date_end: str, semaphore: asyncio.Semaphore):
    base_output_dir = partner_edrpou
    print(f"Запуск процесса загрузки для партнёра {partner_edrpou} с {date_from} по {date_end}")
    print(f"Файлы будут сохранены в базовую папку: ./{base_output_dir}")

    date_ranges = split_date_range_by_month(date_from, date_end)
    all_documents = []
    for start_chunk, end_chunk in date_ranges:
        chunk_from_str = start_chunk.strftime('%Y/%m/%d')
        chunk_end_str = end_chunk.strftime('%Y/%m/%d')
        print(f"Запрос списка документов за период: {chunk_from_str} - {chunk_end_str}")
        url = (f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetPrimaryReestr?"
               f"idOrg={ID_ORG}&docType=-1&moveType=0&dateFrom={chunk_from_str}&dateEnd={chunk_end_str}")
        documents_chunk = await fetch_one_url(session, url, semaphore)
        if documents_chunk:
            all_documents.extend(documents_chunk)
        else:
            logging.warning(f"Не удалось получить документы за период {chunk_from_str} - {chunk_end_str}.")
        await asyncio.sleep(1)

    if not all_documents:
        logging.warning("Не удалось получить данные о документах ни за один из периодов.")
        return

    partner_docs = [doc for doc in all_documents if doc.get('partner_edrpou') == partner_edrpou]

    if not partner_docs:
        print(f"Для партнёра {partner_edrpou} не найдены документы в указанном диапазоне дат.")
        return

    grouped_by_id = defaultdict(list)
    for doc in partner_docs:
        if doc.get('doc_id'):
            grouped_by_id[doc['doc_id']].append(doc)

    unique_partner_docs = []
    for doc_id, doc_group in grouped_by_id.items():
        if len(doc_group) == 1:
            unique_partner_docs.append(doc_group[0])
        else:
            try:
                sorted_group = sorted(
                    doc_group,
                    key=lambda d: datetime.fromisoformat(d.get('moddate', '1970-01-01T00:00:00')),
                    reverse=True
                )
                unique_partner_docs.append(sorted_group[0])
            except (ValueError, TypeError) as e:
                logging.warning(
                    f"Ошибка при сортировке дубликатов для doc_id {doc_id} (Ошибка: {e}). Будет использован первый найденный.")
                unique_partner_docs.append(doc_group[0])

    print(f"Всего найдено в реестре (с дубликатами): {len(partner_docs)} документов.")
    print(f"Найдено уникальных документов (с учетом 'moddate'): {len(unique_partner_docs)}. Начинаю загрузку PDF...")

    tasks = []
    for doc in unique_partner_docs:
        task = get_document_as_pdf(session, doc, semaphore, facsimile=True)
        tasks.append(task)

    results = await asyncio.gather(*tasks)

    failed_docs = []
    successful_count = 0
    for doc, result_path in zip(unique_partner_docs, results):
        if result_path:
            successful_count += 1
        else:
            failed_docs.append(doc)

    total_files_on_disk = 0
    if os.path.exists(base_output_dir):
        for root, dirs, files in os.walk(base_output_dir):
            total_files_on_disk += len(files)

    print("\n" + "=" * 40)
    print("--- ИТОГИ ЗАГРУЗКИ ---")
    print(f"Найдено в реестре (с дубликатами): {len(partner_docs)}")
    print(f"Найдено уникальных документов: {len(unique_partner_docs)}")
    print(f"✅ Успешно загружено по данным скрипта: {successful_count}")
    print(f"💽 Фактически файлов в папках: {total_files_on_disk}")

    if failed_docs:
        failed_ids = [d.get('doc_id', 'N/A') for d in failed_docs]
        print(f"❌ Не удалось загрузить: {len(failed_docs)} файлов")
        print(f"   ID незагруженных документов: {failed_ids}")
    print("=" * 40 + "\n")
    print("🎉 Все задачи по загрузке завершены.")


async def main_async(partner, date_from, date_to):
    """Асинхронная точка входа, вызываемая из GUI."""
    semaphore = asyncio.Semaphore(5)
    async with aiohttp.ClientSession() as session:
        await download_documents_for_partner(
            session=session,
            partner_edrpou=partner,
            date_from=date_from,
            date_end=date_to,
            semaphore=semaphore
        )


# ==============================================================================
# --- БЛОК ГРАФИЧЕСКОГО ИНТЕРФЕЙСА (GUI) ---
# ==============================================================================

class QueueWriter:
    """Класс для перенаправления вывода (print) в очередь для GUI."""

    def __init__(self, queue):
        self.queue = queue

    def write(self, text):
        self.queue.put(text)

    def flush(self):
        pass


class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Загрузчик документов M.E.Doc")
        self.root.geometry("700x500")

        # Стили
        style = ttk.Style(self.root)
        style.theme_use("clam")
        style.configure("TLabel", padding=5, font=("Helvetica", 10))
        style.configure("TEntry", padding=5, font=("Helvetica", 10))
        style.configure("TButton", padding=5, font=("Helvetica", 10, "bold"))

        # Фрейм для полей ввода
        input_frame = ttk.Frame(self.root, padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # Поля ввода
        ttk.Label(input_frame, text="Код партнера (ЄДРПОУ):").grid(row=0, column=0, sticky=tk.W)
        self.partner_entry = ttk.Entry(input_frame, width=40)
        self.partner_entry.grid(row=0, column=1, sticky=tk.EW)

        ttk.Label(input_frame, text="Дата с (ДД.ММ.ГГГГ):").grid(row=1, column=0, sticky=tk.W)
        self.date_from_entry = ttk.Entry(input_frame)
        self.date_from_entry.grid(row=1, column=1, sticky=tk.EW)

        ttk.Label(input_frame, text="Дата по (ДД.ММ.ГГГГ):").grid(row=2, column=0, sticky=tk.W)
        self.date_to_entry = ttk.Entry(input_frame)
        self.date_to_entry.grid(row=2, column=1, sticky=tk.EW)

        input_frame.columnconfigure(1, weight=1)

        # Установка дат по умолчанию
        today = datetime.today()
        start_of_year = today.replace(month=1, day=1)
        self.date_from_entry.insert(0, start_of_year.strftime("%d.%m.%Y"))
        self.date_to_entry.insert(0, today.strftime("%d.%m.%Y"))

        # Кнопка
        self.download_button = ttk.Button(self.root, text="Скачать документы", command=self.start_download_thread)
        self.download_button.pack(fill=tk.X, padx=10, pady=10)

        # Поле для вывода лога
        self.log_text = scrolledtext.ScrolledText(self.root, wrap=tk.WORD, font=("Courier New", 9))
        self.log_text.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        self.log_text.configure(state='disabled')

        # Очередь для обмена сообщениями между потоками
        self.log_queue = queue.Queue()
        self.root.after(100, self.process_queue)

    def start_download_thread(self):
        """Запускает процесс скачивания в отдельном потоке."""
        partner = self.partner_entry.get().strip()
        date_from_str = self.date_from_entry.get().strip()
        date_to_str = self.date_to_entry.get().strip()

        if not all([partner, date_from_str, date_to_str]):
            messagebox.showerror("Ошибка", "Все поля должны быть заполнены!")
            return

        try:
            # Преобразуем дату в формат, который нужен скрипту
            date_from = datetime.strptime(date_from_str, "%d.%m.%Y").strftime("%Y/%m/%d")
            date_to = datetime.strptime(date_to_str, "%d.%m.%Y").strftime("%Y/%m/%d")
        except ValueError:
            messagebox.showerror("Ошибка", "Неверный формат даты. Используйте ДД.ММ.ГГГГ.")
            return

        self.download_button.config(state=tk.DISABLED, text="Загрузка...")
        self.log_text.configure(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state='disabled')

        # Создаем и запускаем поток
        self.thread = threading.Thread(
            target=self.run_async_task,
            args=(partner, date_from, date_to)
        )
        self.thread.daemon = True
        self.thread.start()

    def run_async_task(self, partner, date_from, date_to):
        """
        Эта функция выполняется в фоновом потоке.
        Она перенаправляет stdout и запускает asyncio цикл.
        """
        # Сохраняем оригинальный stdout
        original_stdout = sys.stdout
        # Перенаправляем stdout в нашу очередь
        sys.stdout = QueueWriter(self.log_queue)

        try:
            asyncio.run(main_async(partner, date_from, date_to))
        except Exception as e:
            # Логируем любые ошибки, которые могли произойти
            print(f"\n!!! КРИТИЧЕСКАЯ ОШИБКА В ФОНОВОМ ПОТОКЕ !!!")
            print(f"Тип ошибки: {type(e).__name__}")
            print(f"Сообщение: {e}")
        finally:
            # Возвращаем stdout на место и сообщаем GUI о завершении
            sys.stdout = original_stdout
            self.log_queue.put("---END---")

    def process_queue(self):
        """
        Проверяет очередь и выводит сообщения в текстовое поле GUI.
        Выполняется в основном потоке Tkinter.
        """
        try:
            while True:
                msg = self.log_queue.get_nowait()
                if msg == "---END---":
                    self.download_button.config(state=tk.NORMAL, text="Скачать документы")
                else:
                    self.log_text.configure(state='normal')
                    self.log_text.insert(tk.END, msg)
                    self.log_text.see(tk.END)  # Автопрокрутка
                    self.log_text.configure(state='disabled')
        except queue.Empty:
            pass

        # Повторяем проверку через 100 мс
        self.root.after(100, self.process_queue)


if __name__ == '__main__':
    root = tk.Tk()
    app = App(root)
    root.mainloop()