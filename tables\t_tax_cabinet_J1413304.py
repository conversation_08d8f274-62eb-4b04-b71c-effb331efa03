# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1413304"
SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
      id uuid DEFAULT uuid_generate_v4() NOT NULL,
      hdate date NULL, -- Дата Повідомлення
      hnum varchar(30) NULL, -- Но<PERSON><PERSON>р Повідомлення
      htin int8 NULL, -- ОКПО/паспорт
      hnpdv int8 NULL, -- ИНН
      hname varchar(100) NULL, -- Назва
      r01g1s varchar NULL, -- комісія
      r01g2d date NULL, -- Дата реєстрації таблиці
      r01g3s int8 NULL, -- Номер реєстрації
      r02g1d date NULL, -- Дата рішення
      r02g2s varchar(20) NULL, -- Номер рішення
      m021 numeric(1) NULL, -- наявність ризикових операцій
      m022 numeric(1) NULL, -- невідповідность видів діяльності
      m023 numeric(1) NULL, -- інше
      r023g1s varchar(20) NULL, -- інше
      hexecutor varchar(50) NULL, -- голова комісії
      filename varchar(55) NULL,
      CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 7. РІШЕННЯ про неврахування таблиці даних';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.htin IS 'ОКПО/паспорт';
    COMMENT ON COLUMN {TABLE_NAME}.hnpdv IS 'ИНН';
    COMMENT ON COLUMN {TABLE_NAME}.hname IS 'Назва';
    COMMENT ON COLUMN {TABLE_NAME}.r01g1s IS 'комісія';
    COMMENT ON COLUMN {TABLE_NAME}.r01g2d IS 'Дата реєстрації таблиці';
    COMMENT ON COLUMN {TABLE_NAME}.r01g3s IS 'Номер реєстрації';
    COMMENT ON COLUMN {TABLE_NAME}.r02g1d IS 'Дата рішення';
    COMMENT ON COLUMN {TABLE_NAME}.r02g2s IS 'Номер рішення';
    COMMENT ON COLUMN {TABLE_NAME}.m021 IS 'наявність ризикових операцій';
    COMMENT ON COLUMN {TABLE_NAME}.m022 IS 'невідповідность видів діяльності';
    COMMENT ON COLUMN {TABLE_NAME}.m023 IS 'інше';
    COMMENT ON COLUMN {TABLE_NAME}.r023g1s IS 'інше';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';
"""


async def main_t_tax_cabinet_j1413304_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1413304_async())
