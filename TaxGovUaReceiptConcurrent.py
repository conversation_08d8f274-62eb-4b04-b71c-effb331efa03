import os
import time
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.chrome.options import Options

from TaxGovUaConfig import authorizations, get_max_page_number, get_table_data, go_to_page, service

cur_dir = os.path.dirname(os.path.abspath(__file__))
chrome_options = Options()
download_dir = os.path.join(cur_dir, r"downloads\XML")
os.makedirs(download_dir, exist_ok=True)
prefs = {
    # "download.prompt_for_download": True,  # Всегда указывать место для скачивания
    # "download.directory_upgrade": True,  # Обновление каталога загрузки
    "profile.default_content_settings.popups": 0,  # Отключение всплывающих окон
    "download.prompt_for_download": False,  # Отключение запроса на загрузку
    "download.default_directory": download_dir,  # Путь для сохранения файлов
    "profile.content_settings.exceptions.automatic_downloads.*.setting": 1,  # Разрешить множественные загрузки
}
chrome_options.add_experimental_option("prefs", prefs)
thread_count = 10  # Количество потоков


# Функция для обработки группы страниц
def process_group(page_group, group_id):
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Запуск авторизации
    authorizations(driver)

    # Открываем сайт
    driver.get("https://cabinet.tax.gov.ua/documents/in")
    time.sleep(10 * thread_count)  # Ожидание загрузки страницы
    try:
        for page in page_group:
            print(f"Обработка страницы {page} в группе {group_id} {datetime.now()}")
            if go_to_page(driver, page):
                get_table_data(driver)
            else:
                break
            time.sleep(1)  # Небольшая задержка для стабильности
    finally:
        driver.quit()


# Основная функция для разделения страниц и их обработки
def main(thread_count):
    import concurrent.futures
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Получаем максимальный номер страницы
    max_page_number = get_max_page_number(driver)
    if max_page_number is None:
        return

    pages = list(range(1, max_page_number))  # Пример массива номеров страниц
    groups = [pages[i::thread_count] for i in range(thread_count)]  # Разделение на 6 групп

    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for i, group in enumerate(groups):
            futures.append(executor.submit(process_group, group, i))
            time.sleep(13)  # Интервал в 30 секунд между запуском потоков
        concurrent.futures.wait(futures)


if __name__ == "__main__":
    main(thread_count)
