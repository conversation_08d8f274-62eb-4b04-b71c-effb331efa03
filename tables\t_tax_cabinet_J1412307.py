# таблица для хранения данных из сайта https://tax.gov.ua/ (НН, РК регистриция которых приостановлена)

import asyncio
import os
import sys
from pathlib import Path
from unittest import result

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_j1412307"
SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
      id uuid DEFAULT uuid_generate_v4() NOT NULL,
      hdate date NULL, -- Дата Повідомлення
      hnum varchar(30) NULL, -- Номер Повідомлення
      htin int8 NULL, -- ОКПО/паспорт
      hnpdv numeric(15) NULL, -- ИНН
      hname varchar(100) NULL, -- Назва
      r01g1d date NULL, -- Дата реєстрації таблиці
      r01g2s int8 NULL, -- Номер реєстрації
      m010 numeric(1) NULL, -- врахування
      m020 numeric(1) NULL, -- неврахування
      m021 numeric(1) NULL, -- наявність ризикових операцій
      t21rxxxxg1s numeric(3) NULL, -- Тип операції
      t21rxxxxg21d date NULL, -- Дата з
      t21rxxxxg22d date NULL, -- Дата по
      t21rxxxxg3s varchar(20) NULL, -- УКТЗЕД
      t21rxxxxg4s varchar(20) NULL, -- ДКПП
      t21rxxxxg5s varchar(20) NULL, -- Умовний код товару
      t21rxxxxg6s varchar(20) NULL, -- Податковий номер платника
      t21rxxxxg7d date NULL, -- Дата вкл до ризику
      m024 numeric(1) NULL, -- ризиковий
      r024g1s varchar(20) NULL, -- пункт
      m022 numeric(1) NULL, -- невідповідність видів діяльності
      r022g1s varchar(255) NULL, -- розшифрування
      m023 numeric(1) NULL, -- інше
      r023g1s varchar(200) NULL, -- інше
      hexecutor varchar(50) NULL, -- голова комісії
      filename varchar(55) NULL,
      CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Додаток 6. РІШЕННЯ про врахування/неврахування таблиці даних';
    COMMENT ON COLUMN {TABLE_NAME}.hdate IS 'Дата Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.hnum IS 'Номер Повідомлення';
    COMMENT ON COLUMN {TABLE_NAME}.htin IS 'ОКПО/паспорт';
    COMMENT ON COLUMN {TABLE_NAME}.hnpdv IS 'ИНН';
    COMMENT ON COLUMN {TABLE_NAME}.hname IS 'Назва';
    COMMENT ON COLUMN {TABLE_NAME}.r01g1d IS 'Дата реєстрації таблиці';
    COMMENT ON COLUMN {TABLE_NAME}.r01g2s IS 'Номер реєстрації';
    COMMENT ON COLUMN {TABLE_NAME}.m010 IS 'врахування';
    COMMENT ON COLUMN {TABLE_NAME}.m020 IS 'неврахування';
    COMMENT ON COLUMN {TABLE_NAME}.m021 IS 'наявність ризикових операцій';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg1s IS 'Тип операції';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg21d IS 'Дата з';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg22d IS 'Дата по';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg3s IS 'УКТЗЕД';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg4s IS 'ДКПП';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg5s IS 'Умовний код товару';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg6s IS 'Податковий номер платника';
    COMMENT ON COLUMN {TABLE_NAME}.t21rxxxxg7d IS 'Дата вкл до ризику';
    COMMENT ON COLUMN {TABLE_NAME}.m024 IS 'ризиковий';
    COMMENT ON COLUMN {TABLE_NAME}.r024g1s IS 'пункт';
    COMMENT ON COLUMN {TABLE_NAME}.m022 IS 'невідповідність видів діяльності';
    COMMENT ON COLUMN {TABLE_NAME}.r022g1s IS 'розшифрування';
    COMMENT ON COLUMN {TABLE_NAME}.m023 IS 'інше';
    COMMENT ON COLUMN {TABLE_NAME}.r023g1s IS 'інше';
    COMMENT ON COLUMN {TABLE_NAME}.hexecutor IS 'голова комісії';


"""


async def main_t_tax_cabinet_j1412307_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_j1412307_async())
