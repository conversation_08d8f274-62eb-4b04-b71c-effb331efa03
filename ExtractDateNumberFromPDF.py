import re

import pdfplumber


def get_doc_info(pdf_path):
    # Открываем PDF файл
    with pdfplumber.open(pdf_path) as pdf:
        # Проходим по всем страницам
        for page in pdf.pages:
            # Извлекаем текст со страницы
            text = page.extract_text()
            if text:
                # Ищем строки, соответствующие шаблону
                # matches = re.findall(r"від (\d{2}\.\d{2}\.\d{4} № ?\d+(?:/\d+)?)", text)
                matches = re.findall(r"(догов|накл[А-Яа-яёЁЇїІіЄєҐґ]*)? від (\d{2}\.\d{2}\.\d{4} № ?\d+(?:/\d+)?)+", text)
                for match in matches:
                    d = list(match)
                    d[1] = list(match)[-1].replace('№ ', '№')
                    match  =' '.join(d)
                    print(match)
                    save_to_txt_file(match)


def save_to_txt_file(text):
    with open("120465345.txt", "a", encoding="utf-8") as file:
        file.write(text + "\n")


if __name__ == '__main__':
    pdf_path = r"C:\Users\<USER>\Desktop\sud\140_1868_24\Єдиний державний реєстр судових рішень 120465345.pdf"
    get_doc_info(pdf_path)
