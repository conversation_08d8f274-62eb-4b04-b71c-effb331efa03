import os
import pandas as pd
from AsyncPostgresql import engine

cur_dir = os.path.dirname(os.path.abspath(__file__))
save_to_dir = os.path.join(cur_dir, "downloads", "pdf", "erpn_code")


def get_data_from_db():
    sql = f"""
      SELECT
          code,
          cptin
      FROM t_tax_cabinet_erpn_api
      WHERE kvt4 IN (1)
      ;
    """
    df = pd.read_sql_query(sql, engine)
    return df

def create_folder(folder):
    if not os.path.exists(folder):
        os.makedirs(folder)
    return folder

def move_files_to_folder(df):
    for index, row in df.iterrows():
        code = row['code']
        cptin = row['cptin']
        old_file = os.path.join(save_to_dir, f"{code} 4.pdf")
        folder = os.path.join(save_to_dir, str(cptin))
        create_folder(folder)
        new_file = os.path.join(folder, f"{code}.pdf")
        os.rename(old_file, new_file)
    return folder


if __name__ == "__main__":
    df = get_data_from_db()
    df['path'] = df.apply(lambda x: f"{x['code']} 4.pdf", axis=1)
    move_files_to_folder(df)
    print(df.head())
