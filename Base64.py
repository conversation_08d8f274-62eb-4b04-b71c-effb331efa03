import base64

string_to_encode = "Hello, world!"
encoded_bytes = base64.b64encode(string_to_encode.encode("utf-8"))
encoded_string = encoded_bytes.decode("utf-8")

print(encoded_string)
encoded_string = '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'
decoded_bytes = base64.b64decode(encoded_string)
decoded_string = decoded_bytes.decode("utf-8", errors='ignore')

print(decoded_string)
