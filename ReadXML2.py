import re
import os
from unittest import result

# Функция для чтения XML файла и возвращения его содержимого в виде строки
def read_xml_file(file_path):
    encodings = ["utf-8", "windows-1251", "cp1251"]
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
        except UnicodeDecodeError:
            continue
    raise UnicodeDecodeError(f"Не удалось декодировать файл {file_path} с использованием кодировок {encodings}")


def trim_element_in_list(lst):
    d = [x.strip() for x in lst]
    return [i for i in d if i != ""]


def get_ipns(lst):
    result = []
    for i in lst:
        mathes = re.search("\d{12}", i)
        if mathes:
            result.append(mathes[0])
    return result

def get_resolutions_number(lst):
    for i in lst:
        mathes = re.search("\d+/\d+", i)
        if mathes:
            return mathes.group(0)
    return None

# Функция для извлечения всех текстовых данных с использованием регулярных выражений
def extract_all_text_with_regex(xml_content):
    # Регулярное выражение для поиска всех текстовых данных между тегами
    regex_pattern = r'>([^<]+)<'
    regex = re.compile(regex_pattern)
    matches = regex.findall(xml_content)    
    matches = trim_element_in_list(matches)
    return matches


# Функция для получения списка файлов с определенным расширением
def get_files_by_ext(path, ext):
    return [f for f in os.listdir(path) if f.endswith(ext)]


if __name__ == "__main__":
    cur_dir = os.path.dirname(__file__)
    folder_path = os.path.join(cur_dir, "downloads", "XML")
    files = get_files_by_ext(folder_path, ".xml")
    
    for file in files:
        print(f"Processing file: {file}")
        xml_file_path = os.path.join(folder_path, file)
        
        # Чтение XML файла
        xml_content = read_xml_file(xml_file_path)        

        # Извлечение всех текстовых данных с использованием регулярного выражения
        extracted_data = extract_all_text_with_regex(xml_content)
        ipns = get_ipns(extracted_data)
        print(ipns)
        resolutions_number = get_resolutions_number(extracted_data)
        print(resolutions_number)
        pass
        
        # Продолжение обработки данных
        # Например, создание DataFrame и добавление данных в PostgreSQL
        # ...
