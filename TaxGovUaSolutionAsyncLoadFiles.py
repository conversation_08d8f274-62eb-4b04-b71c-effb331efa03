# сначала запускается обработчик (TaxGovUaSolutionAsync), для скачивания всех решений
# перед загрузкой файлов, скрипт отбирает файлы, наименования которых отсутствуют в базе данных
# затем запускается этот скрипт, который загружает этот файл/решение.
# Какой тип файлов скачивать (xml или pdf) указывается в переменной file_ext
# данный скрипт промежуточный, только для скачивания файлов/решений
# после него надо запустить скрипт для загруки инф о решениях в базу данных
import asyncio
import os
from pathlib import Path

import aiohttp
import pandas as pd
import requests

from AsyncPostgresql import async_save_pg, get_df
from TaxGovUaConfig import get_token
from TaxGovUaSolutionAsync import main_load_solutions_list_to_db_async

url_const = r"https://cabinet.tax.gov.ua/ws/api/regdoc/doc/"
current_path = os.path.dirname(os.path.abspath(__file__))
download_path = os.path.join(current_path, "downloads", "pdf", "solution")
os.makedirs(download_path, exist_ok=True)
file_ext = "xml"  # "pdf"

SQL_UPDATE_FILENAME = """
    UPDATE t_tax_cabinet_solutions
    SET filename = $1
    WHERE id = $2;
"""


async def save_all_filenames_to_df():
    pdf_files_list_without_path = [
        file for file in os.listdir(download_path) if file.endswith(f".{file_ext}")
    ]
    data = [(file, int(Path(file).stem)) for file in pdf_files_list_without_path]
    result = await async_save_pg(SQL_UPDATE_FILENAME, data)
    if not result:
        print("Ошибка сохранения имен файлов в базу данных")


async def get_solutions_df() -> pd.DataFrame:
    sql = """
    SELECT DISTINCT
        id, periodyear, datein
    FROM t_tax_cabinet_solutions 
    WHERE name ilike '%РIШЕННЯ%' 
        AND COALESCE(filename,'') = ''
    ORDER BY datein DESC;
    """
    df = await get_df(sql)
    return df


async def load_solutions_async(url, save_pdf_to_path, retries=5):
    global token
    while retries > 0:
        try:
            response = requests.get(url, headers={"Authorization": token}, timeout=10)
            if response.status_code == 200:
                content = response.content
                with open(save_pdf_to_path, "wb") as f:
                    f.write(content)
                if os.path.exists(save_pdf_to_path):
                    print(f"Файл сохранен: {save_pdf_to_path}")
                    return
        except Exception as e:
            retries -= 1
            # print(f"Ошибка при загрузке файла {url}: {e}")
            pass
        driver, token = get_token()


async def get_solutions(df):
    async with aiohttp.ClientSession() as session:
        urls = {
            rf"{url_const}{row['periodyear']}/{row['id']}/{file_ext}": rf"{download_path}\{row['id']}.{file_ext}"
            for _, row in df.iterrows()
        }
        tasks = [
            load_solutions_async(url, save_pdf_to_path)
            for url, save_pdf_to_path in urls.items()
        ]
        return await asyncio.gather(*tasks)


async def main_load_solutions_fields_async():
    global token
    await main_load_solutions_list_to_db_async()
    await save_all_filenames_to_df()  # заполняем имена файлов в базе данных
    df = await get_solutions_df()
    if not df.empty:
        driver, token = get_token()
        await get_solutions(df)
        driver.quit()
    await save_all_filenames_to_df()


if __name__ == "__main__":
    asyncio.run(main_load_solutions_fields_async())
